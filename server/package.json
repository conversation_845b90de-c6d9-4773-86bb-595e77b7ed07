{"name": "elysia-nuxt-admin", "version": "1.0.0", "description": "A modern admin dashboard built with Nuxt 3, Elysia.js, and shadcn-vue", "author": "nightwhite", "license": "MIT", "keywords": ["nuxt", "elysia", "admin", "dashboard", "vue", "typescript", "shadcn-vue"], "repository": {"type": "git", "url": "https://github.com/nightwhite/elysia-nuxt-admin.git"}, "private": false, "type": "module", "scripts": {"build": "bun --bun nuxt build", "dev": "bun --bun --max-old-space-size=512 --expose-gc nuxt dev", "generate": "bun --bun nuxt generate", "preview": "bun --bun nuxt preview", "postinstall": "bun --bun nuxt prepare", "server": "HTTP_PROXY=http://127.0.0.1:7890 HTTPS_PROXY=http://127.0.0.1:7890 NODE_ENV=development bun --max-old-space-size=512 --expose-gc run server/index.ts", "setup": "cp .env.example .env && echo '✅ 环境变量文件已创建，请编辑 .env 文件配置你的设置'", "clean": "rm -rf .nuxt .output dist node_modules/.cache", "type-check": "nuxt typecheck"}, "dependencies": {"@aws-sdk/client-s3": "^3.832.0", "@aws-sdk/s3-request-presigner": "^3.832.0", "@elysiajs/bearer": "^1.3.0", "@elysiajs/cors": "^1.3.3", "@elysiajs/eden": "^1.3.2", "@elysiajs/swagger": "^1.3.0", "@prisma/client": "^6.11.1", "@tailwindcss/vite": "^4.1.10", "@tanstack/vue-table": "^8.21.3", "@vueuse/core": "^13.4.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "elysia": "^1.3.4", "elysia-autoload": "^1.5.2", "google-auth-library": "^10.1.0", "jose": "^6.0.11", "lucide-vue-next": "^0.514.0", "mime-types": "^3.0.1", "nuxt": "^3.17.5", "prisma": "^6.11.1", "reka-ui": "^2.3.1", "resend": "^6.0.1", "shadcn-nuxt": "^2.2.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "tw-animate-css": "^1.3.4", "vue": "^3.5.16", "vue-router": "^4.5.1"}, "devDependencies": {"@types/mime-types": "^3.0.1", "bun-types": "^1.2.16", "nuxt-elysia": "^0.2.0"}}