<template>
  <Dialog :open="isOpen" @update:open="$emit('update:isOpen', $event)">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <Ban class="h-5 w-5 text-orange-600" />
          确认停用邀请码
        </DialogTitle>
        <DialogDescription>
          确定要停用邀请码 "{{ itemName }}" 吗？
        </DialogDescription>
      </DialogHeader>

      <!-- 错误提示 -->
      <div v-if="error" class="rounded-md bg-destructive/15 p-3">
        <div class="flex">
          <AlertCircle class="h-5 w-5 text-destructive" />
          <div class="ml-3">
            <h3 class="text-sm font-medium text-destructive">停用失败</h3>
            <p class="mt-1 text-sm text-destructive">{{ error }}</p>
          </div>
        </div>
      </div>

      <!-- 提示信息 -->
      <div class="rounded-md bg-blue-50 p-3 border border-blue-200">
        <div class="flex">
          <Info class="h-5 w-5 text-blue-600" />
          <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">停用说明</h3>
            <div class="mt-1 text-sm text-blue-700">
              <ul class="list-disc list-inside space-y-1">
                <li>停用后用户将无法使用此邀请码注册</li>
                <li>已使用此邀请码注册的用户不受影响</li>
                <li>您可以随时重新启用此邀请码</li>
                <li>停用不会删除邀请码，只是暂时禁用</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <DialogFooter>
        <Button
          variant="outline"
          @click="$emit('update:isOpen', false)"
          :disabled="deactivating"
        >
          取消
        </Button>
        <Button
          variant="secondary"
          @click="$emit('confirm')"
          :disabled="deactivating"
        >
          <div v-if="deactivating" class="flex items-center gap-2">
            <div class="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
            <span>停用中...</span>
          </div>
          <div v-else class="flex items-center gap-2">
            <Ban class="h-4 w-4" />
            <span>确认停用</span>
          </div>
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { Button } from '~/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import { Ban, AlertCircle, Info } from 'lucide-vue-next'

interface Props {
  isOpen: boolean
  deactivating?: boolean
  error?: string | null
  itemName: string
}

interface Emits {
  (e: 'update:isOpen', value: boolean): void
  (e: 'confirm'): void
}

withDefaults(defineProps<Props>(), {
  deactivating: false,
  error: null,
})

defineEmits<Emits>()
</script>
