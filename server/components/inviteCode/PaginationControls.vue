<template>
  <div class="flex flex-col gap-4 pt-4 md:flex-row md:items-center md:justify-between">
    <!-- 分页信息 -->
    <div class="text-sm text-muted-foreground">
      <span v-if="pagination.total > 0">
        显示第 {{ startItem }} - {{ endItem }} 项，共 {{ pagination.total }} 项
      </span>
      <span v-else>
        {{ filterApplied ? '没有找到符合条件的记录' : '暂无数据' }}
      </span>
    </div>

    <!-- 分页控件 -->
    <div v-if="pagination.total > 0" class="flex flex-col gap-2 md:flex-row md:items-center">
      <!-- 每页显示数量选择 -->
      <div class="flex items-center gap-2">
        <span class="text-sm text-muted-foreground">每页显示</span>
        <Select :model-value="pagination.pageSize.toString()" @update:model-value="handlePageSizeChange">
          <SelectTrigger class="w-20">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="10">10</SelectItem>
            <SelectItem value="20">20</SelectItem>
            <SelectItem value="50">50</SelectItem>
            <SelectItem value="100">100</SelectItem>
          </SelectContent>
        </Select>
        <span class="text-sm text-muted-foreground">项</span>
      </div>

      <!-- 分页按钮 -->
      <div class="flex items-center gap-1">
        <!-- 首页 -->
        <Button
          variant="outline"
          size="icon"
          @click="goToPage(1)"
          :disabled="pagination.page === 1"
          class="h-8 w-8"
        >
          <ChevronsLeft class="h-4 w-4" />
        </Button>

        <!-- 上一页 -->
        <Button
          variant="outline"
          size="icon"
          @click="goToPage(pagination.page - 1)"
          :disabled="pagination.page === 1"
          class="h-8 w-8"
        >
          <ChevronLeft class="h-4 w-4" />
        </Button>

        <!-- 页码按钮 -->
        <div class="flex items-center gap-1">
          <template v-for="page in visiblePages" :key="page">
            <Button
              v-if="page === '...'"
              variant="ghost"
              size="icon"
              disabled
              class="h-8 w-8"
            >
              ...
            </Button>
            <Button
              v-else
              :variant="page === pagination.page ? 'default' : 'outline'"
              size="icon"
              @click="goToPage(page as number)"
              class="h-8 w-8"
            >
              {{ page }}
            </Button>
          </template>
        </div>

        <!-- 下一页 -->
        <Button
          variant="outline"
          size="icon"
          @click="goToPage(pagination.page + 1)"
          :disabled="pagination.page === pagination.totalPages"
          class="h-8 w-8"
        >
          <ChevronRight class="h-4 w-4" />
        </Button>

        <!-- 末页 -->
        <Button
          variant="outline"
          size="icon"
          @click="goToPage(pagination.totalPages)"
          :disabled="pagination.page === pagination.totalPages"
          class="h-8 w-8"
        >
          <ChevronsRight class="h-4 w-4" />
        </Button>
      </div>

      <!-- 跳转到指定页 -->
      <div class="flex items-center gap-2">
        <span class="text-sm text-muted-foreground">跳转到</span>
        <Input
          v-model="jumpToPage"
          type="number"
          :min="1"
          :max="pagination.totalPages"
          class="w-16 h-8"
          @keyup.enter="handleJumpToPage"
        />
        <Button
          variant="outline"
          size="sm"
          @click="handleJumpToPage"
          :disabled="!isValidJumpPage"
          class="h-8"
        >
          跳转
        </Button>
      </div>
    </div>

    <!-- 无数据时的操作 -->
    <div v-else-if="filterApplied" class="flex justify-center">
      <Button @click="$emit('reset-filters')" variant="outline" size="sm">
        清除筛选条件
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Button } from '~/components/ui/button'
import { Input } from '~/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from 'lucide-vue-next'

interface Pagination {
  page: number
  pageSize: number
  total: number
  totalPages: number
}

interface Props {
  pagination: Pagination
  filterApplied?: boolean
}

interface Emits {
  (e: 'go-to-page', page: number): void
  (e: 'change-page-size', pageSize: number): void
  (e: 'reset-filters'): void
}

const props = withDefaults(defineProps<Props>(), {
  filterApplied: false,
})

const emit = defineEmits<Emits>()

// 跳转页码
const jumpToPage = ref('')

// 计算属性
const startItem = computed(() => {
  if (props.pagination.total === 0) return 0
  return (props.pagination.page - 1) * props.pagination.pageSize + 1
})

const endItem = computed(() => {
  const end = props.pagination.page * props.pagination.pageSize
  return Math.min(end, props.pagination.total)
})

const isValidJumpPage = computed(() => {
  const page = parseInt(jumpToPage.value)
  return page >= 1 && page <= props.pagination.totalPages
})

// 计算可见的页码
const visiblePages = computed(() => {
  const current = props.pagination.page
  const total = props.pagination.totalPages
  const pages: (number | string)[] = []

  if (total <= 7) {
    // 总页数少于等于7页，显示所有页码
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // 总页数大于7页，智能显示页码
    if (current <= 4) {
      // 当前页在前面
      for (let i = 1; i <= 5; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    } else if (current >= total - 3) {
      // 当前页在后面
      pages.push(1)
      pages.push('...')
      for (let i = total - 4; i <= total; i++) {
        pages.push(i)
      }
    } else {
      // 当前页在中间
      pages.push(1)
      pages.push('...')
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    }
  }

  return pages
})

// 跳转到指定页
const goToPage = (page: number) => {
  if (page >= 1 && page <= props.pagination.totalPages && page !== props.pagination.page) {
    emit('go-to-page', page)
  }
}

// 处理每页显示数量变化
const handlePageSizeChange = (value: string) => {
  const pageSize = parseInt(value)
  if (pageSize !== props.pagination.pageSize) {
    emit('change-page-size', pageSize)
  }
}

// 处理跳转到指定页
const handleJumpToPage = () => {
  if (isValidJumpPage.value) {
    const page = parseInt(jumpToPage.value)
    goToPage(page)
    jumpToPage.value = ''
  }
}
</script>
