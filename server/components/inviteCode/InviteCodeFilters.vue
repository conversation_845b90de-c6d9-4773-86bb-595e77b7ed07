<template>
  <div class="flex flex-col gap-4 mb-6">
    <!-- 搜索和筛选行 -->
    <div class="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
      <!-- 搜索框 -->
      <div class="flex-1 max-w-md">
        <div class="relative">
          <Search class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            v-model="searchValue"
            placeholder="搜索邀请码..."
            class="pl-10"
            @input="handleSearchInput"
            @keyup.enter="handleSearch"
          />
        </div>
      </div>

      <!-- 筛选和操作按钮 -->
      <div class="flex flex-col gap-2 md:flex-row md:items-center">
        <!-- 状态筛选 -->
        <Select v-model="statusValue" @update:model-value="handleStatusChange">
          <SelectTrigger class="w-full md:w-40">
            <SelectValue placeholder="状态筛选" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">全部状态</SelectItem>
            <SelectItem value="active">可用</SelectItem>
            <SelectItem value="inactive">已停用</SelectItem>
            <SelectItem value="expired">已过期</SelectItem>
            <SelectItem value="exhausted">已用完</SelectItem>
          </SelectContent>
        </Select>

        <!-- 刷新按钮 -->
        <Button
          variant="outline"
          size="icon"
          @click="handleRefresh"
          :disabled="loading || isRefreshing"
          class="shrink-0"
        >
          <RotateCcw 
            class="h-4 w-4" 
            :class="{ 'animate-spin': isRefreshing }"
          />
        </Button>
      </div>
    </div>

    <!-- 活动筛选指示器 -->
    <div v-if="hasActiveFilters" class="flex items-center gap-2 text-sm text-muted-foreground">
      <Filter class="h-4 w-4" />
      <span>已应用筛选条件</span>
      <Button
        variant="ghost"
        size="sm"
        @click="handleClearFilters"
        class="h-auto p-1 text-xs underline"
      >
        清除筛选
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Input } from '~/components/ui/input'
import { Button } from '~/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import { Search, RotateCcw, Filter } from 'lucide-vue-next'

interface Props {
  loading?: boolean
  isRefreshing?: boolean
  isFiltering?: boolean
  initialSearch?: string
  initialStatus?: string
}

interface Emits {
  (e: 'search', query: string): void
  (e: 'filter-change', filters: { status: string }): void
  (e: 'refresh'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  isRefreshing: false,
  isFiltering: false,
  initialSearch: '',
  initialStatus: '',
})

const emit = defineEmits<Emits>()

// 响应式数据
const searchValue = ref(props.initialSearch)
const statusValue = ref(props.initialStatus)

// 搜索防抖
let searchTimeout: NodeJS.Timeout | null = null

// 计算属性
const hasActiveFilters = computed(() => {
  return !!(searchValue.value || statusValue.value)
})

// 监听初始值变化
watch(() => props.initialSearch, (newValue) => {
  searchValue.value = newValue
})

watch(() => props.initialStatus, (newValue) => {
  statusValue.value = newValue
})

// 搜索输入处理（防抖）
const handleSearchInput = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  
  searchTimeout = setTimeout(() => {
    handleSearch()
  }, 500) // 500ms 防抖
}

// 搜索处理
const handleSearch = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
    searchTimeout = null
  }
  emit('search', searchValue.value.trim())
}

// 状态筛选处理
const handleStatusChange = () => {
  emit('filter-change', {
    status: statusValue.value
  })
}

// 刷新处理
const handleRefresh = () => {
  emit('refresh')
}

// 清除筛选
const handleClearFilters = () => {
  searchValue.value = ''
  statusValue.value = ''
  
  // 触发搜索和筛选事件
  emit('search', '')
  emit('filter-change', { status: '' })
}

// 重置筛选（供父组件调用）
const resetFilters = () => {
  searchValue.value = ''
  statusValue.value = ''
}

// 暴露方法给父组件
defineExpose({
  resetFilters
})
</script>
