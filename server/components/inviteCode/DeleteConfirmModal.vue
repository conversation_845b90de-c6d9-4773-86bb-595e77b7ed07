<template>
  <Dialog :open="isOpen" @update:open="$emit('update:isOpen', $event)">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <AlertTriangle class="h-5 w-5 text-destructive" />
          确认删除{{ itemType }}
        </DialogTitle>
        <DialogDescription>
          此操作无法撤销。确定要删除{{ itemType }} "{{ itemName }}" 吗？
        </DialogDescription>
      </DialogHeader>

      <!-- 错误提示 -->
      <div v-if="error" class="rounded-md bg-destructive/15 p-3">
        <div class="flex">
          <AlertCircle class="h-5 w-5 text-destructive" />
          <div class="ml-3">
            <h3 class="text-sm font-medium text-destructive">删除失败</h3>
            <p class="mt-1 text-sm text-destructive">{{ error }}</p>
          </div>
        </div>
      </div>

      <!-- 警告信息 -->
      <div class="rounded-md bg-yellow-50 p-3 border border-yellow-200">
        <div class="flex">
          <AlertTriangle class="h-5 w-5 text-yellow-600" />
          <div class="ml-3">
            <h3 class="text-sm font-medium text-yellow-800">注意</h3>
            <div class="mt-1 text-sm text-yellow-700">
              <ul class="list-disc list-inside space-y-1">
                <li>删除后该邀请码将无法恢复</li>
                <li>已使用此邀请码注册的用户不受影响</li>
                <li>正在使用此邀请码的注册流程可能会失败</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <DialogFooter>
        <Button
          variant="outline"
          @click="$emit('update:isOpen', false)"
          :disabled="deleting"
        >
          取消
        </Button>
        <Button
          variant="destructive"
          @click="$emit('confirm')"
          :disabled="deleting"
        >
          <div v-if="deleting" class="flex items-center gap-2">
            <div class="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
            <span>删除中...</span>
          </div>
          <div v-else class="flex items-center gap-2">
            <Trash2 class="h-4 w-4" />
            <span>确认删除</span>
          </div>
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { Button } from '~/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import { AlertTriangle, AlertCircle, Trash2 } from 'lucide-vue-next'

interface Props {
  isOpen: boolean
  deleting?: boolean
  error?: string | null
  itemName: string
  itemType?: string
}

interface Emits {
  (e: 'update:isOpen', value: boolean): void
  (e: 'confirm'): void
}

withDefaults(defineProps<Props>(), {
  deleting: false,
  error: null,
  itemType: '项目',
})

defineEmits<Emits>()
</script>
