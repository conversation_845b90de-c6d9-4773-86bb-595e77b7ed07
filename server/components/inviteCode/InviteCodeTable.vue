<template>
  <div class="rounded-md border">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center h-40">
      <div class="flex items-center gap-2">
        <div class="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
        <span class="text-sm text-muted-foreground">加载中...</span>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="flex flex-col items-center justify-center h-40 gap-4">
      <div class="text-center">
        <AlertCircle class="h-8 w-8 text-destructive mx-auto mb-2" />
        <p class="text-sm text-muted-foreground mb-2">{{ error }}</p>
        <Button @click="$emit('retry')" size="sm">
          <RotateCcw class="h-4 w-4 mr-2" />
          重试
        </Button>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="inviteCodes.length === 0" class="flex flex-col items-center justify-center h-40 gap-4">
      <div class="text-center">
        <Ticket class="h-8 w-8 text-muted-foreground mx-auto mb-2" />
        <p class="text-sm text-muted-foreground mb-2">
          {{ filterApplied ? '没有找到符合条件的邀请码' : '还没有邀请码' }}
        </p>
        <div class="flex gap-2">
          <Button v-if="filterApplied" @click="$emit('reset-filters')" variant="outline" size="sm">
            清除筛选
          </Button>
          <Button @click="$emit('add-invite-code')" size="sm">
            <Plus class="h-4 w-4 mr-2" />
            创建邀请码
          </Button>
        </div>
      </div>
    </div>

    <!-- 邀请码表格 -->
    <div v-else class="overflow-x-auto">
      <table class="w-full">
        <thead>
          <tr class="border-b bg-muted/50">
            <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              邀请码
            </th>
            <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              创建者
            </th>
            <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              使用情况
            </th>
            <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              状态
            </th>
            <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              过期时间
            </th>
            <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              创建时间
            </th>
            <th class="h-12 px-4 text-right align-middle font-medium text-muted-foreground">
              操作
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="inviteCode in inviteCodes"
            :key="inviteCode.id"
            class="border-b transition-colors hover:bg-muted/50"
          >
            <!-- 邀请码 -->
            <td class="p-4 align-middle">
              <div class="flex items-center gap-2">
                <code class="relative rounded bg-muted px-2 py-1 font-mono text-sm">
                  {{ inviteCode.code }}
                </code>
                <Button
                  variant="ghost"
                  size="icon"
                  class="h-6 w-6"
                  @click="copyToClipboard(inviteCode.code)"
                >
                  <Copy class="h-3 w-3" />
                </Button>
              </div>
            </td>

            <!-- 创建者 -->
            <td class="p-4 align-middle">
              <div class="flex flex-col">
                <span class="font-medium">{{ inviteCode.creator?.username || '未知' }}</span>
                <span class="text-xs text-muted-foreground">{{ inviteCode.creator?.email || '' }}</span>
              </div>
            </td>

            <!-- 使用情况 -->
            <td class="p-4 align-middle">
              <div class="flex items-center gap-2">
                <div class="flex flex-col">
                  <span class="text-sm font-medium">
                    {{ inviteCode.usedCount }} / {{ inviteCode.maxUses }}
                  </span>
                  <div class="w-20 bg-muted rounded-full h-1.5">
                    <div
                      class="bg-primary h-1.5 rounded-full transition-all"
                      :style="{ width: `${Math.min((inviteCode.usedCount / inviteCode.maxUses) * 100, 100)}%` }"
                    ></div>
                  </div>
                </div>
              </div>
            </td>

            <!-- 状态 -->
            <td class="p-4 align-middle">
              <Badge :variant="getStatusVariant(inviteCode)">
                {{ getStatusText(inviteCode) }}
              </Badge>
            </td>

            <!-- 过期时间 -->
            <td class="p-4 align-middle">
              <span v-if="inviteCode.expiresAt" class="text-sm">
                {{ formatDate(inviteCode.expiresAt) }}
              </span>
              <span v-else class="text-sm text-muted-foreground">永不过期</span>
            </td>

            <!-- 创建时间 -->
            <td class="p-4 align-middle">
              <span class="text-sm">{{ formatDate(inviteCode.createdAt) }}</span>
            </td>

            <!-- 操作 -->
            <td class="p-4 align-middle text-right">
              <div class="flex items-center justify-end gap-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" class="h-8 w-8">
                      <MoreHorizontal class="h-4 w-4" />
                      <span class="sr-only">打开菜单</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem @click="$emit('edit-invite-code', inviteCode)">
                      <Edit class="h-4 w-4 mr-2" />
                      编辑
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      v-if="inviteCode.isActive"
                      @click="$emit('deactivate-invite-code', inviteCode)"
                    >
                      <Ban class="h-4 w-4 mr-2" />
                      停用
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      @click="$emit('delete-invite-code', inviteCode)"
                      class="text-destructive"
                    >
                      <Trash2 class="h-4 w-4 mr-2" />
                      删除
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Button } from '~/components/ui/button'
import { Badge } from '~/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu'
import {
  AlertCircle,
  RotateCcw,
  Ticket,
  Plus,
  Copy,
  MoreHorizontal,
  Edit,
  Ban,
  Trash2,
} from 'lucide-vue-next'
import type { InviteCode } from '~/composables/useInviteCodeApi'

interface Props {
  inviteCodes: InviteCode[]
  loading?: boolean
  error?: string | null
  filterApplied?: boolean
}

interface Emits {
  (e: 'edit-invite-code', inviteCode: InviteCode): void
  (e: 'delete-invite-code', inviteCode: InviteCode): void
  (e: 'deactivate-invite-code', inviteCode: InviteCode): void
  (e: 'add-invite-code'): void
  (e: 'reset-filters'): void
  (e: 'retry'): void
}

defineProps<Props>()
defineEmits<Emits>()

// 获取状态变体
const getStatusVariant = (inviteCode: InviteCode) => {
  if (!inviteCode.isActive) return 'secondary'
  if (inviteCode.expiresAt && new Date(inviteCode.expiresAt) < new Date()) return 'destructive'
  if (inviteCode.usedCount >= inviteCode.maxUses) return 'outline'
  return 'default'
}

// 获取状态文本
const getStatusText = (inviteCode: InviteCode) => {
  if (!inviteCode.isActive) return '已停用'
  if (inviteCode.expiresAt && new Date(inviteCode.expiresAt) < new Date()) return '已过期'
  if (inviteCode.usedCount >= inviteCode.maxUses) return '已用完'
  return '可用'
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    const message = useMessage()
    message.success('复制成功', '邀请码已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    const message = useMessage()
    message.error('复制失败', '无法复制到剪贴板')
  }
}
</script>
