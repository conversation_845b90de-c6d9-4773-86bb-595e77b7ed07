<template>
  <Dialog :open="isOpen" @update:open="$emit('update:isOpen', $event)">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>
          {{ inviteCode ? '编辑邀请码' : '创建邀请码' }}
        </DialogTitle>
        <DialogDescription>
          {{ inviteCode ? '修改邀请码设置' : '创建新的邀请码供用户注册使用' }}
        </DialogDescription>
      </DialogHeader>

      <form @submit.prevent="handleSubmit" class="space-y-4">
        <!-- 错误提示 -->
        <div v-if="error" class="rounded-md bg-destructive/15 p-3">
          <div class="flex">
            <AlertCircle class="h-5 w-5 text-destructive" />
            <div class="ml-3">
              <h3 class="text-sm font-medium text-destructive">操作失败</h3>
              <p class="mt-1 text-sm text-destructive">{{ error }}</p>
            </div>
          </div>
        </div>

        <!-- 编辑模式：显示邀请码信息 -->
        <div v-if="inviteCode" class="space-y-4">
          <div>
            <Label>邀请码</Label>
            <div class="flex items-center gap-2 mt-1">
              <code class="flex-1 rounded bg-muted px-3 py-2 font-mono text-sm">
                {{ inviteCode.code }}
              </code>
              <Button
                type="button"
                variant="outline"
                size="icon"
                @click="copyToClipboard(inviteCode.code)"
              >
                <Copy class="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label>使用情况</Label>
              <p class="text-sm text-muted-foreground mt-1">
                {{ inviteCode.usedCount }} / {{ inviteCode.maxUses }}
              </p>
            </div>
            <div>
              <Label>创建时间</Label>
              <p class="text-sm text-muted-foreground mt-1">
                {{ formatDate(inviteCode.createdAt) }}
              </p>
            </div>
          </div>

          <div>
            <div class="flex items-center space-x-2">
              <Checkbox
                id="isActive"
                v-model:checked="formData.isActive"
              />
              <Label for="isActive">启用邀请码</Label>
            </div>
            <p class="text-xs text-muted-foreground mt-1">
              停用后用户将无法使用此邀请码注册
            </p>
          </div>
        </div>

        <!-- 创建模式：邀请码设置 -->
        <div v-else class="space-y-4">
          <div>
            <Label for="customCode">自定义邀请码（可选）</Label>
            <Input
              id="customCode"
              v-model="formData.customCode"
              placeholder="留空将自动生成"
              class="mt-1"
            />
            <p class="text-xs text-muted-foreground mt-1">
              只能包含字母、数字和下划线，长度6-20位
            </p>
          </div>

          <div>
            <Label for="maxUses">最大使用次数</Label>
            <Input
              id="maxUses"
              v-model.number="formData.maxUses"
              type="number"
              min="1"
              max="999"
              placeholder="1"
              class="mt-1"
              required
            />
            <p class="text-xs text-muted-foreground mt-1">
              设置此邀请码最多可以被使用的次数
            </p>
          </div>

          <div>
            <Label for="expiresAt">过期时间（可选）</Label>
            <Input
              id="expiresAt"
              v-model="formData.expiresAt"
              type="datetime-local"
              class="mt-1"
            />
            <p class="text-xs text-muted-foreground mt-1">
              留空表示永不过期
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            @click="$emit('update:isOpen', false)"
            :disabled="saving"
          >
            取消
          </Button>
          <Button type="submit" :disabled="saving">
            <div v-if="saving" class="flex items-center gap-2">
              <div class="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
              <span>{{ inviteCode ? '保存中...' : '创建中...' }}</span>
            </div>
            <span v-else>{{ inviteCode ? '保存' : '创建' }}</span>
          </Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { Button } from '~/components/ui/button'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { Checkbox } from '~/components/ui/checkbox'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import { AlertCircle, Copy } from 'lucide-vue-next'
import type { InviteCode } from '~/composables/useInviteCodeApi'

interface Props {
  isOpen: boolean
  inviteCode?: InviteCode | null
  saving?: boolean
  error?: string | null
}

interface Emits {
  (e: 'update:isOpen', value: boolean): void
  (e: 'save', data: any): void
}

const props = withDefaults(defineProps<Props>(), {
  saving: false,
  error: null,
})

const emit = defineEmits<Emits>()

// 表单数据
const formData = ref({
  customCode: '',
  maxUses: 1,
  expiresAt: '',
  isActive: true,
})

// 监听邀请码变化，重置表单
watch(() => props.inviteCode, (newInviteCode) => {
  if (newInviteCode) {
    // 编辑模式
    formData.value = {
      customCode: '',
      maxUses: newInviteCode.maxUses,
      expiresAt: newInviteCode.expiresAt ? 
        new Date(newInviteCode.expiresAt).toISOString().slice(0, 16) : '',
      isActive: newInviteCode.isActive,
    }
  } else {
    // 创建模式
    formData.value = {
      customCode: '',
      maxUses: 1,
      expiresAt: '',
      isActive: true,
    }
  }
}, { immediate: true })

// 监听模态框打开状态，重置错误
watch(() => props.isOpen, (isOpen) => {
  if (!isOpen) {
    // 模态框关闭时重置表单
    formData.value = {
      customCode: '',
      maxUses: 1,
      expiresAt: '',
      isActive: true,
    }
  }
})

// 提交表单
const handleSubmit = () => {
  const data = {
    ...formData.value,
    expiresAt: formData.value.expiresAt || undefined,
  }

  if (props.inviteCode) {
    // 编辑模式：只发送可编辑的字段
    emit('save', {
      id: props.inviteCode.id,
      isActive: data.isActive,
    })
  } else {
    // 创建模式：发送所有字段
    emit('save', {
      customCode: data.customCode || undefined,
      maxUses: data.maxUses,
      expiresAt: data.expiresAt,
    })
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    const message = useMessage()
    message.success('复制成功', '邀请码已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    const message = useMessage()
    message.error('复制失败', '无法复制到剪贴板')
  }
}
</script>
