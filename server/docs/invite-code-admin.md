# 邀请码管理功能 - 服务器管理端

## 📋 功能概述

为服务器管理端添加了完整的邀请码管理功能，管理员可以通过Web界面创建、查看、编辑和删除邀请码。

## 🎯 已完成的功能

### 1. 管理页面
- **页面路径**: `/inviteCodeManager`
- **菜单位置**: 主导航菜单中的"邀请码管理"
- **权限要求**: 管理员权限

### 2. 核心功能
- ✅ **邀请码列表**: 分页显示所有邀请码
- ✅ **搜索筛选**: 支持按邀请码、创建者搜索，按状态筛选
- ✅ **创建邀请码**: 支持自定义邀请码、使用次数、过期时间
- ✅ **编辑邀请码**: 启用/停用邀请码
- ✅ **删除邀请码**: 永久删除邀请码
- ✅ **停用邀请码**: 临时停用邀请码
- ✅ **复制邀请码**: 一键复制到剪贴板
- ✅ **使用情况统计**: 显示使用进度和状态

### 3. 状态管理
- **可用**: 邀请码激活且未过期未用完
- **已停用**: 管理员手动停用
- **已过期**: 超过设定的过期时间
- **已用完**: 达到最大使用次数

## 📁 文件结构

```
server/
├── pages/inviteCodeManager/
│   └── index.vue                    # 邀请码管理主页面
├── components/inviteCode/
│   ├── InviteCodeFilters.vue        # 搜索筛选组件
│   ├── InviteCodeTable.vue          # 邀请码表格组件
│   ├── InviteCodeModal.vue          # 创建/编辑模态框
│   ├── PaginationControls.vue       # 分页控件
│   ├── DeleteConfirmModal.vue       # 删除确认模态框
│   └── DeactivateConfirmModal.vue   # 停用确认模态框
├── composables/
│   └── useInviteCodeApi.ts          # 邀请码API接口
├── components/ui/badge/             # Badge组件（新增）
│   ├── Badge.vue
│   └── index.ts
└── scripts/
    └── add-invite-code-menu.ts      # 添加菜单脚本
```

## 🔧 后端API扩展

### 新增管理员API路由

1. **GET** `/api/invite-codes/list` - 分页获取邀请码列表
   - 支持搜索、状态筛选、创建者筛选
   - 返回分页数据和统计信息

2. **PUT** `/api/invite-codes/:id` - 更新邀请码状态
   - 支持启用/停用邀请码

3. **DELETE** `/api/invite-codes/:id` - 删除邀请码
   - 永久删除指定邀请码

4. **POST** `/api/invite-codes/:id/deactivate` - 停用邀请码
   - 临时停用邀请码

5. **POST** `/api/invite-codes/batch` - 批量创建邀请码
   - 支持批量创建多个邀请码

## 🚀 部署步骤

### 1. 添加菜单项
```bash
cd server
bun run scripts/add-invite-code-menu.ts
```

### 2. 验证功能
1. 启动服务器: `bun run dev`
2. 以管理员身份登录
3. 访问"邀请码管理"菜单
4. 测试创建、编辑、删除功能

## 🎨 界面特性

### 响应式设计
- 支持桌面端和移动端
- 自适应布局和交互

### 用户体验
- 实时搜索（500ms防抖）
- 加载状态指示
- 错误处理和提示
- 确认对话框防误操作

### 数据展示
- 邀请码状态徽章
- 使用进度条
- 创建者信息
- 时间格式化

## 🔐 权限控制

- **页面访问**: 需要管理员权限
- **API调用**: 所有管理API都需要管理员权限
- **菜单显示**: 只有管理员能看到邀请码管理菜单

## 📊 数据统计

管理页面显示：
- 邀请码总数
- 各状态邀请码数量
- 使用情况统计
- 创建者分布

## 🔄 与现有系统集成

- 复用现有的认证系统
- 使用统一的UI组件库
- 遵循现有的代码规范
- 集成到现有的菜单系统

## 📝 使用说明

### 创建邀请码
1. 点击"创建邀请码"按钮
2. 可选择自定义邀请码或自动生成
3. 设置最大使用次数
4. 可选设置过期时间
5. 点击"创建"完成

### 管理邀请码
1. 在列表中找到目标邀请码
2. 点击操作菜单
3. 选择编辑、停用或删除
4. 确认操作

### 搜索筛选
1. 使用搜索框输入邀请码或创建者信息
2. 使用状态下拉框筛选特定状态的邀请码
3. 点击刷新按钮更新数据

## 🐛 注意事项

1. 删除邀请码是永久操作，无法恢复
2. 停用邀请码可以重新启用
3. 已使用的邀请码记录会保留
4. 搜索功能支持模糊匹配
5. 分页大小可以调整（10/20/50/100）

## 🔮 后续扩展

可以考虑添加的功能：
- 邀请码使用统计图表
- 批量操作（批量停用/删除）
- 邀请码模板功能
- 导出邀请码列表
- 邀请码使用日志
