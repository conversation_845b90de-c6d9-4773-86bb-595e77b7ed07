import { prisma } from '../server/db/prisma';
import { hashPassword } from '../server/utils/crypto';
import { createInviteCode } from '../server/services/inviteCodeService';

async function initInviteSystem() {
  try {
    console.log('🚀 初始化邀请码系统...');

    // 检查是否已有管理员用户
    let adminUser = await prisma.user.findFirst({
      where: { role: 'admin' }
    });

    if (!adminUser) {
      console.log('📝 创建管理员用户...');
      
      // 创建管理员用户
      const hashedPassword = await hashPassword('Admin@123');
      adminUser = await prisma.user.create({
        data: {
          username: 'admin',
          email: '<EMAIL>',
          password: hashedPassword,
          role: 'admin',
          emailVerified: true
        }
      });
      
      console.log('✅ 管理员用户创建成功:', {
        username: adminUser.username,
        email: adminUser.email,
        id: adminUser.id
      });
    } else {
      console.log('✅ 管理员用户已存在:', {
        username: adminUser.username,
        email: adminUser.email,
        id: adminUser.id
      });
    }

    // 检查是否已有邀请码
    const existingCodes = await prisma.inviteCode.count();
    
    if (existingCodes === 0) {
      console.log('📝 创建初始邀请码...');
      
      // 创建一些初始邀请码
      const inviteCodes = [];
      
      // 创建5个单次使用的邀请码
      for (let i = 0; i < 5; i++) {
        const inviteCode = await createInviteCode({
          createdBy: adminUser.id,
          maxUses: 1
        });
        inviteCodes.push(inviteCode);
      }
      
      // 创建2个多次使用的邀请码
      for (let i = 0; i < 2; i++) {
        const inviteCode = await createInviteCode({
          createdBy: adminUser.id,
          maxUses: 10
        });
        inviteCodes.push(inviteCode);
      }
      
      // 创建一个永久邀请码（用于测试）
      const permanentCode = await createInviteCode({
        createdBy: adminUser.id,
        maxUses: 999,
        customCode: 'WELCOME2024'
      });
      inviteCodes.push(permanentCode);
      
      console.log('✅ 邀请码创建成功:');
      inviteCodes.forEach((code, index) => {
        console.log(`  ${index + 1}. ${code.code} (最大使用次数: ${code.maxUses})`);
      });
      
      console.log('\n🎯 测试邀请码: WELCOME2024 (可使用999次)');
      
    } else {
      console.log(`✅ 已存在 ${existingCodes} 个邀请码`);
      
      // 显示一些可用的邀请码
      const availableCodes = await prisma.inviteCode.findMany({
        where: {
          isActive: true,
          usedCount: {
            lt: prisma.inviteCode.fields.maxUses
          }
        },
        take: 5,
        orderBy: {
          createdAt: 'desc'
        }
      });
      
      if (availableCodes.length > 0) {
        console.log('\n🎯 可用的邀请码:');
        availableCodes.forEach((code, index) => {
          console.log(`  ${index + 1}. ${code.code} (已使用: ${code.usedCount}/${code.maxUses})`);
        });
      }
    }

    console.log('\n✅ 邀请码系统初始化完成!');
    console.log('\n📋 使用说明:');
    console.log('1. 用户注册时必须提供有效的邀请码');
    console.log('2. 管理员可以在后台创建和管理邀请码');
    console.log('3. 每个邀请码都有使用次数限制');
    console.log('4. 邀请码可以设置过期时间');
    console.log('\n🔗 管理员登录信息:');
    console.log('  用户名: admin');
    console.log('  密码: Admin@123');
    console.log('  邮箱: <EMAIL>');

  } catch (error) {
    console.error('❌ 初始化邀请码系统失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 运行初始化
initInviteSystem()
  .then(() => {
    console.log('\n🎉 初始化完成，可以开始使用邀请码系统了！');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 初始化失败:', error);
    process.exit(1);
  });

export { initInviteSystem };
