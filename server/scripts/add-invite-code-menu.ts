import { prisma } from '../server/db/prisma';

async function addInviteCodeMenu() {
  try {
    console.log('🚀 开始添加邀请码管理菜单...');

    // 检查邀请码管理菜单是否已存在
    const existingMenu = await prisma.menu.findFirst({
      where: { title: '邀请码管理' }
    });

    if (existingMenu) {
      console.log('✅ 邀请码管理菜单已存在:', existingMenu);
      return;
    }

    // 获取当前最大的排序值
    const maxSortOrder = await prisma.menu.findFirst({
      where: { parentId: null },
      orderBy: { sortOrder: 'desc' },
      select: { sortOrder: true }
    });

    const newSortOrder = (maxSortOrder?.sortOrder || 0) + 1;

    // 创建邀请码管理菜单
    const inviteCodeMenu = await prisma.menu.create({
      data: {
        title: '邀请码管理',
        path: '/inviteCodeManager',
        icon: 'Ticket',
        sortOrder: newSortOrder,
        parentId: null
      }
    });

    console.log('✅ 邀请码管理菜单创建成功:', inviteCodeMenu);

    // 验证菜单是否创建成功
    const allMenus = await prisma.menu.findMany({
      where: { parentId: null },
      orderBy: { sortOrder: 'asc' }
    });

    console.log('\n📋 当前所有主菜单:');
    allMenus.forEach((menu) => {
      console.log(`  - ${menu.title} (路径: ${menu.path}, 排序: ${menu.sortOrder})`);
    });

    console.log('\n🎉 邀请码管理菜单添加完成！');

  } catch (error) {
    console.error('❌ 添加邀请码管理菜单失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
addInviteCodeMenu()
  .then(() => {
    console.log('\n✨ 脚本执行完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
