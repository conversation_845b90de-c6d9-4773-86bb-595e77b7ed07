import { useApi } from './useApi'

/**
 * 邀请码数据类型
 */
export interface InviteCode {
  id: string
  code: string
  createdBy: string
  usedBy?: string
  maxUses: number
  usedCount: number
  expiresAt?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  creator?: {
    id: string
    username: string
    email: string
  }
  usedByUser?: {
    id: string
    username: string
    email: string
  }
}

/**
 * 邀请码列表查询参数
 */
export interface InviteCodeListParams {
  page?: number
  pageSize?: number
  search?: string
  status?: string // 'active', 'inactive', 'expired', 'exhausted'
  createdBy?: string
}

/**
 * 邀请码列表响应
 */
export interface InviteCodeListResponse {
  inviteCodes: InviteCode[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

/**
 * 创建邀请码参数
 */
export interface CreateInviteCodeParams {
  maxUses?: number
  expiresAt?: string
  customCode?: string
}

/**
 * 更新邀请码参数
 */
export interface UpdateInviteCodeParams {
  isActive?: boolean
}

/**
 * 邀请码统计信息
 */
export interface InviteCodeStats {
  total: number
  active: number
  inactive: number
  expired: number
  exhausted: number
  totalUsed: number
}

/**
 * 邀请码 API 服务
 */
export const useInviteCodeApi = () => {
  const api = useApi()

  /**
   * 获取分页邀请码列表
   */
  const getInviteCodesPaginated = (params: InviteCodeListParams) => {
    const queryParams = new URLSearchParams()
    
    if (params.page) queryParams.append('page', params.page.toString())
    if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString())
    if (params.search) queryParams.append('search', params.search)
    if (params.status) queryParams.append('status', params.status)
    if (params.createdBy) queryParams.append('createdBy', params.createdBy)

    return api.get<InviteCodeListResponse>(`/api/invite-codes/list?${queryParams.toString()}`)
  }

  /**
   * 获取当前用户的邀请码
   */
  const getUserInviteCodes = () => {
    return api.get<InviteCode[]>('/api/invite-codes')
  }

  /**
   * 获取邀请码统计信息
   */
  const getInviteCodeStats = () => {
    return api.get<InviteCodeStats>('/api/invite-codes/stats')
  }

  /**
   * 根据 ID 获取邀请码
   */
  const getInviteCodeById = (id: string) => {
    return api.get<InviteCode>(`/api/invite-codes/${id}`)
  }

  /**
   * 创建邀请码
   */
  const createInviteCode = (data: CreateInviteCodeParams) => {
    return api.post<{ success: boolean; inviteCode: InviteCode }>('/api/invite-codes', data)
  }

  /**
   * 更新邀请码
   */
  const updateInviteCode = (id: string, data: UpdateInviteCodeParams) => {
    return api.put<{ success: boolean }>(`/api/invite-codes/${id}`, data)
  }

  /**
   * 删除邀请码
   */
  const deleteInviteCode = (id: string) => {
    return api.delete<{ success: boolean }>(`/api/invite-codes/${id}`)
  }

  /**
   * 停用邀请码
   */
  const deactivateInviteCode = (id: string) => {
    return api.post<{ success: boolean }>(`/api/invite-codes/${id}/deactivate`)
  }

  /**
   * 验证邀请码
   */
  const validateInviteCode = (code: string) => {
    return api.post<{ 
      success: boolean
      valid: boolean
      inviteCode?: InviteCode
      message?: string
    }>('/api/users/validate-invite-code', { inviteCode: code })
  }

  return {
    getInviteCodesPaginated,
    getUserInviteCodes,
    getInviteCodeStats,
    getInviteCodeById,
    createInviteCode,
    updateInviteCode,
    deleteInviteCode,
    deactivateInviteCode,
    validateInviteCode,
  }
}
