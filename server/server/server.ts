import { bearer } from "@elysiajs/bearer"
import { cors } from "@elysiajs/cors"
import { swagger } from "@elysiajs/swagger"
import { Elysia } from "elysia"

// 导入数据库初始化函数
import { initDB } from "./db/prisma"

// 导入路由文件
import { admin } from "./routes/admin"
import { users } from "./routes/users"
import { menus } from "./routes/menus"
import { s3 } from "./routes/s3"
import { s3Files } from "./routes/s3-files"
import { systemConfig } from "./routes/systemConfig"
import { applicationsRoutes } from "./routes/applications"
import { conversationsRoutes } from "./routes/conversations"
import { frameworksRoutes } from "./routes/frameworks"
import { inviteCodeRoutes } from "./routes/inviteCodes"

// 初始化数据库
initDB().catch(console.error)

// 创建主应用实例
export const app = new Elysia()

// 只在开发模式下加载 Swagger
if (process.env.NODE_ENV === 'development') {
  app.use(swagger({
    documentation: {
      info: {
        title: "Admin Template API",
        description: "基于 Nuxt + Elysia + shadcn-vue 的管理系统模板 API",
        version: "1.0.0",
        contact: {
          name: "Admin Template Team",
        },
      },
      servers: [
        {
          url: "/",  // 使用相对路径，更灵活
          description: "开发环境",
        }
      ],
      tags: [
        {
          name: "基础接口",
          description: "基础服务接口",
        },
        {
          name: "管理接口",
          description: "管理相关接口",
        },
        {
          name: "用户接口",
          description: "用户管理接口",
        },
        {
          name: "菜单接口",
          description: "菜单管理接口",
        },
        {
          name: "S3配置",
          description: "S3存储配置接口",
        },
        {
          name: "S3文件管理",
          description: "S3文件管理接口",
        },
        {
          name: "系统配置",
          description: "系统配置管理接口",
        },
        {
          name: "应用管理",
          description: "AI开发应用管理接口",
        },
        {
          name: "AI对话",
          description: "AI对话和代码生成接口",
        },
      ],
      components: {
        securitySchemes: {
          bearerAuth: {
            type: "http",
            scheme: "bearer",
            bearerFormat: "JWT"
          }
        }
      },
      security: [
        {
          bearerAuth: []
        }
      ]
    }
  }))
  console.log("📚 Swagger documentation loaded (development mode)")
}

app
  .use(bearer())
  .use(cors({
    origin: ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:5175', 'http://localhost:3001', 'http://localhost:3000'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
  }))
  .get("/", "Hello Admin", {
    detail: {
      tags: ["基础接口"],
      summary: "服务健康检查",
      description: "检查服务是否正常运行",
      responses: {
        200: {
          description: "服务正常",
          content: {
            "text/plain": {
              schema: {
                type: "string",
                example: "Hello Admin",
              },
            },
          },
        },
      },
    },
  })
  .get("/hello", () => ({ message: "Hello world!" }), {
    detail: {
      tags: ["基础接口"],
      summary: "Hello World 接口",
      description: "返回 Hello World 消息",
      responses: {
        200: {
          description: "成功响应",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  message: {
                    type: "string",
                    example: "Hello world!",
                  },
                },
              },
            },
          },
        },
      },
    },
  })
  // 使用各种路由模块
  .use(admin)
  .use(users)
  .use(menus)
  .use(s3)
  .use(s3Files)
  .use(systemConfig)
  // AI开发平台路由
  .use(applicationsRoutes)
  .use(conversationsRoutes)
  .use(frameworksRoutes)
  .use(inviteCodeRoutes)

console.log("Elysia server routes loaded")

// 内存监控
const logMemoryUsage = () => {
  const usage = process.memoryUsage();
  const formatBytes = (bytes: number) => (bytes / 1024 / 1024).toFixed(2) + ' MB';

  console.log(`内存使用情况:
    RSS: ${formatBytes(usage.rss)}
    Heap Used: ${formatBytes(usage.heapUsed)}
    Heap Total: ${formatBytes(usage.heapTotal)}
    External: ${formatBytes(usage.external)}`);

  // 如果内存使用超过500MB，强制垃圾回收
  if (usage.heapUsed > 500 * 1024 * 1024) {
    console.log('⚠️ 内存使用过高，触发垃圾回收');
    if (global.gc) {
      global.gc();
    }
  }
};

// 每5分钟记录一次内存使用情况
setInterval(logMemoryUsage, 5 * 60 * 1000);

// 立即记录一次
logMemoryUsage();

// 优雅关闭处理
import { closeDB } from "./db/prisma"

const gracefulShutdown = async (signal: string) => {
  console.log(`\n收到 ${signal} 信号，开始优雅关闭...`);

  try {
    // 关闭数据库连接
    await closeDB();
    console.log('✅ 数据库连接已关闭');

    // 退出进程
    process.exit(0);
  } catch (error) {
    console.error('❌ 优雅关闭失败:', error);
    process.exit(1);
  }
};

// 监听进程信号
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// 监听未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  gracefulShutdown('uncaughtException');
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  console.error('Promise:', promise);
  gracefulShutdown('unhandledRejection');
});

export type ElysiaApp = typeof app