import { Elysia, t } from "elysia"
import { prisma } from "../db/prisma"
import { requireAdmin } from "../utils/auth"
import { getSystemInfo } from "../utils/systemConfig"


/**
 * 管理接口路由模块
 */
export const admin = new Elysia()
  .group("/admin", (app) =>
    app
      .use(requireAdmin)
      /**
       * 获取管理系统信息
       */
      .get("/info", async () => {
        const systemInfo = await getSystemInfo()
        return {
          name: systemInfo.name,
          version: systemInfo.version,
          env: process.env.NODE_ENV || "development"
        }
      }, {
    detail: {
      tags: ["管理接口"],
      summary: "获取管理系统信息",
      description: "返回管理系统的基本信息",
      responses: {
        200: {
          description: "成功响应",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  name: { type: "string" },
                  version: { type: "string" },
                  env: { type: "string" }
                }
              }
            }
          }
        }
      }
    }
  })
      /**
       * 获取所有用户（调试接口）
       */
      .get("/debug/users", async () => {
        // 调试接口，获取所有用户（包含密码，仅用于调试）
        const users = await prisma.user.findMany();
        return users;
      }, {
    detail: {
      tags: ["管理接口"],
      summary: "调试: 获取所有用户",
      description: "返回数据库中的所有用户信息（包括密码，仅用于调试）",
    }
  })
      /**
       * 获取仪表盘数据
       */
      .get("/dashboard", async () => {
    // 获取各种统计数据
    const userCount = await prisma.user.count();
    const menuCount = await prisma.menu.count();
    const systemConfigCount = await prisma.systemConfig.count();

    // 获取最近添加的用户
    const recentUsers = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        role: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    });
    
    return {
      stats: {
        userCount,
        menuCount,
        systemConfigCount
      },
      recentUsers: recentUsers.map(user => ({
        ...user,
        created_at: user.createdAt.toISOString()
      }))
    };
  }, {
    detail: {
      tags: ["管理接口"],
      summary: "获取仪表盘数据",
      description: "返回管理系统仪表盘的统计数据",
      responses: {
        200: {
          description: "成功响应",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  stats: {
                    type: "object",
                    properties: {
                      userCount: { type: "number" },
                      menuCount: { type: "number" },
                      roleCount: { type: "number" }
                    }
                  },
                  recentUsers: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        id: { type: "number" },
                        username: { type: "string" },
                        name: { type: "string" },
                        role: { type: "string" },
                        created_at: { type: "string" }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  })
      /**
       * 获取数据库表
       */
      .get("/db/tables", async () => {
    // 获取PostgreSQL数据库中的所有表
    const tables = await prisma.$queryRaw`
      SELECT table_name as name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_type = 'BASE TABLE'
    `;
    return tables;
  }, {
    detail: {
      tags: ["管理接口"],
      summary: "获取数据库表",
      description: "返回数据库中的所有表",
      responses: {
        200: {
          description: "成功响应",
          content: {
            "application/json": {
              schema: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    name: { type: "string" }
                  }
                }
              }
            }
          }
        }
      }
    }
  })
      /**
       * 获取表详情
       */
      .get("/db/table/:name", async ({ params }) => {
    try {
      // 获取PostgreSQL表结构
      const schema = await prisma.$queryRaw`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns
        WHERE table_name = ${params.name}
        AND table_schema = 'public'
        ORDER BY ordinal_position
      `;

      // 获取表数据（限制返回条数）
      const data = await prisma.$queryRawUnsafe(`SELECT * FROM "${params.name}" LIMIT 100`);

      return {
        schema,
        data
      };
    } catch (error: any) {
      return new Response(JSON.stringify({ error: error.message }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    params: t.Object({
      name: t.String()
    }),
    detail: {
      tags: ["管理接口"],
      summary: "获取表详情",
      description: "返回指定表的结构和数据",
      responses: {
        200: {
          description: "成功响应",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  schema: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        cid: { type: "number" },
                        name: { type: "string" },
                        type: { type: "string" },
                        notnull: { type: "number" },
                        dflt_value: { type: "string" },
                        pk: { type: "number" }
                      }
                    }
                  },
                  data: {
                    type: "array",
                    items: {
                      type: "object"
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  })
      /**
       * 执行SQL查询
       */
      .post("/db/query", async ({ body }) => {
    try {
      // 安全检查：只允许SELECT查询
      const sql = body.sql.trim().toLowerCase();
      if (!sql.startsWith('select')) {
        return new Response(JSON.stringify({ error: "只允许SELECT查询" }), {
          status: 400,
          headers: { "Content-Type": "application/json" },
        });
      }

      const result = await prisma.$queryRawUnsafe(body.sql);
      return { success: true, data: result };
    } catch (error: any) {
      return new Response(JSON.stringify({ error: error.message }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    body: t.Object({
      sql: t.String()
    }),
    detail: {
      tags: ["管理接口"],
      summary: "执行SQL查询",
      description: "执行自定义SQL查询（仅限SELECT语句）",
      responses: {
        200: {
          description: "查询成功",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  data: {
                    type: "array",
                    items: {
                      type: "object"
                    }
                  }
                }
              }
            }
          }
        },
        400: {
          description: "查询失败",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: { type: "string" }
                }
              }
            }
          }
        }
      }
    }
  })
  );

export default admin; 