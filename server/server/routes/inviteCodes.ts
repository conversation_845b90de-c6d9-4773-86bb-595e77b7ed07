import { Elysia, t } from "elysia";
import { logger } from "../utils/logger";
import { requireAuth, requireAdmin } from "../utils/auth";
import { prisma } from "../db/prisma";
import {
  createInviteCode,
  getUserInviteCodes,
  getInviteCodeStats,
  deactivateInviteCode,
  validateInviteCode
} from "../services/inviteCodeService";

export const inviteCodeRoutes = new Elysia({ prefix: "/api" })
  .use(requireAuth)

  /**
   * 创建邀请码
   */
  .post("/invite-codes", async ({ body, user }) => {
    const { maxUses, expiresAt, customCode } = body;

    try {
      const inviteCode = await createInviteCode({
        createdBy: user.id,
        maxUses,
        expiresAt: expiresAt ? new Date(expiresAt) : undefined,
        customCode
      });

      return { success: true, inviteCode };
    } catch (error: any) {
      logger.error('创建邀请码失败', { error: error.message, userId: user.id });
      return new Response(JSON.stringify({ error: error.message }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    body: t.Object({
      maxUses: t.Optional(t.Number()),
      expiresAt: t.Optional(t.String()),
      customCode: t.Optional(t.String()),
    }),
    detail: {
      tags: ["邀请码"],
      summary: "创建邀请码",
      description: "创建新的邀请码"
    }
  })

  /**
   * 获取用户的邀请码列表
   */
  .get("/invite-codes", async ({ user }) => {
    try {
      const inviteCodes = await getUserInviteCodes(user.id);
      return { success: true, inviteCodes };
    } catch (error: any) {
      logger.error('获取邀请码列表失败', { error: error.message, userId: user.id });
      return new Response(JSON.stringify({ error: "获取邀请码列表失败" }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    detail: {
      tags: ["邀请码"],
      summary: "获取邀请码列表",
      description: "获取当前用户创建的邀请码列表"
    }
  })

  /**
   * 获取邀请码统计信息
   */
  .get("/invite-codes/stats", async ({ user }) => {
    try {
      const stats = await getInviteCodeStats(user.id);
      return { success: true, stats };
    } catch (error: any) {
      logger.error('获取邀请码统计失败', { error: error.message, userId: user.id });
      return new Response(JSON.stringify({ error: "获取统计信息失败" }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    detail: {
      tags: ["邀请码"],
      summary: "获取邀请码统计",
      description: "获取当前用户的邀请码统计信息"
    }
  })

  /**
   * 禁用邀请码
   */
  .patch("/invite-codes/:code/deactivate", async ({ params, user }) => {
    const { code } = params;

    try {
      const inviteCode = await deactivateInviteCode(code, user.id);
      return { success: true, inviteCode };
    } catch (error: any) {
      logger.error('禁用邀请码失败', { error: error.message, code, userId: user.id });
      return new Response(JSON.stringify({ error: error.message }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    params: t.Object({
      code: t.String(),
    }),
    detail: {
      tags: ["邀请码"],
      summary: "禁用邀请码",
      description: "禁用指定的邀请码"
    }
  })

  /**
   * 管理员：获取所有邀请码
   */
  .use(requireAdmin)
  .get("/invite-codes/list", async ({ query }) => {
    const {
      page = 1,
      pageSize = 10,
      search,
      status,
      createdBy
    } = query;

    try {
      const skip = (Number(page) - 1) * Number(pageSize);

      const where: any = {};

      // 搜索条件
      if (search) {
        where.OR = [
          { code: { contains: search, mode: 'insensitive' } },
          { creator: { username: { contains: search, mode: 'insensitive' } } },
          { creator: { email: { contains: search, mode: 'insensitive' } } }
        ];
      }

      // 状态筛选
      if (status) {
        const now = new Date();
        switch (status) {
          case 'active':
            where.isActive = true;
            where.OR = [
              { expiresAt: null },
              { expiresAt: { gt: now } }
            ];
            where.usedCount = { lt: prisma.inviteCode.fields.maxUses };
            break;
          case 'inactive':
            where.isActive = false;
            break;
          case 'expired':
            where.expiresAt = { lte: now };
            break;
          case 'exhausted':
            where.usedCount = { gte: prisma.inviteCode.fields.maxUses };
            break;
        }
      }

      // 创建者筛选
      if (createdBy) {
        where.createdBy = createdBy;
      }

      const [inviteCodes, total] = await Promise.all([
        prisma.inviteCode.findMany({
          where,
          include: {
            creator: {
              select: {
                id: true,
                username: true,
                email: true
              }
            },
            usedByUser: {
              select: {
                id: true,
                username: true,
                email: true
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: Number(pageSize)
        }),
        prisma.inviteCode.count({ where })
      ]);

      return {
        success: true,
        data: {
          inviteCodes,
          pagination: {
            page: Number(page),
            pageSize: Number(pageSize),
            total,
            totalPages: Math.ceil(total / Number(pageSize))
          }
        }
      };
    } catch (error: any) {
      logger.error('获取邀请码列表失败', { error: error.message });
      return new Response(JSON.stringify({
        success: false,
        message: "获取邀请码列表失败"
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    query: t.Object({
      page: t.Optional(t.String()),
      pageSize: t.Optional(t.String()),
      search: t.Optional(t.String()),
      status: t.Optional(t.String()),
      createdBy: t.Optional(t.String()),
    }),
    detail: {
      tags: ["邀请码"],
      summary: "获取分页邀请码列表（管理员）",
      description: "管理员获取系统中所有邀请码的分页列表"
    }
  })

  /**
   * 管理员：更新邀请码
   */
  .put("/invite-codes/:id", async ({ params, body, user }) => {
    const { id } = params;
    const { isActive } = body;

    try {
      const inviteCode = await prisma.inviteCode.update({
        where: { id },
        data: { isActive },
        include: {
          creator: {
            select: {
              id: true,
              username: true,
              email: true
            }
          }
        }
      });

      logger.info('邀请码已更新', {
        inviteCodeId: id,
        isActive,
        updatedBy: user.id
      });

      return { success: true, inviteCode };
    } catch (error: any) {
      logger.error('更新邀请码失败', {
        error: error.message,
        inviteCodeId: id,
        userId: user.id
      });
      return new Response(JSON.stringify({
        success: false,
        message: error.message || "更新邀请码失败"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    params: t.Object({
      id: t.String(),
    }),
    body: t.Object({
      isActive: t.Boolean(),
    }),
    detail: {
      tags: ["邀请码"],
      summary: "更新邀请码（管理员）",
      description: "管理员更新邀请码状态"
    }
  })

  /**
   * 管理员：删除邀请码
   */
  .delete("/invite-codes/:id", async ({ params, user }) => {
    const { id } = params;

    try {
      await prisma.inviteCode.delete({
        where: { id }
      });

      logger.info('邀请码已删除', {
        inviteCodeId: id,
        deletedBy: user.id
      });

      return { success: true };
    } catch (error: any) {
      logger.error('删除邀请码失败', {
        error: error.message,
        inviteCodeId: id,
        userId: user.id
      });
      return new Response(JSON.stringify({
        success: false,
        message: error.message || "删除邀请码失败"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    params: t.Object({
      id: t.String(),
    }),
    detail: {
      tags: ["邀请码"],
      summary: "删除邀请码（管理员）",
      description: "管理员删除指定的邀请码"
    }
  })

  /**
   * 管理员：停用邀请码
   */
  .post("/invite-codes/:id/deactivate", async ({ params, user }) => {
    const { id } = params;

    try {
      const inviteCode = await prisma.inviteCode.update({
        where: { id },
        data: { isActive: false },
        include: {
          creator: {
            select: {
              id: true,
              username: true,
              email: true
            }
          }
        }
      });

      logger.info('邀请码已停用', {
        inviteCodeId: id,
        deactivatedBy: user.id
      });

      return { success: true, inviteCode };
    } catch (error: any) {
      logger.error('停用邀请码失败', {
        error: error.message,
        inviteCodeId: id,
        userId: user.id
      });
      return new Response(JSON.stringify({
        success: false,
        message: error.message || "停用邀请码失败"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    params: t.Object({
      id: t.String(),
    }),
    detail: {
      tags: ["邀请码"],
      summary: "停用邀请码（管理员）",
      description: "管理员停用指定的邀请码"
    }
  })

  /**
   * 管理员：批量创建邀请码
   */
  .post("/invite-codes/batch", async ({ body, user }) => {
    const { count, maxUses, expiresAt } = body;

    try {
      const inviteCodes = [];
      for (let i = 0; i < count; i++) {
        const inviteCode = await createInviteCode({
          createdBy: user.id,
          maxUses,
          expiresAt: expiresAt ? new Date(expiresAt) : undefined
        });
        inviteCodes.push(inviteCode);
      }

      logger.info('批量创建邀请码成功', {
        count,
        maxUses,
        createdBy: user.id
      });

      return { success: true, inviteCodes };
    } catch (error: any) {
      logger.error('批量创建邀请码失败', {
        error: error.message,
        userId: user.id
      });
      return new Response(JSON.stringify({
        success: false,
        message: error.message || "批量创建邀请码失败"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    body: t.Object({
      count: t.Number({ minimum: 1, maximum: 100 }),
      maxUses: t.Number({ minimum: 1 }),
      expiresAt: t.Optional(t.String())
    }),
    detail: {
      tags: ["邀请码"],
      summary: "批量创建邀请码（管理员）",
      description: "管理员批量创建邀请码"
    }
  });
