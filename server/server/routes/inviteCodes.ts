import { Elysia, t } from "elysia";
import { logger } from "../utils/logger";
import { requireAuth, requireAdmin } from "../utils/auth";
import { prisma } from "../db/prisma";
import {
  createInviteCode,
  getUserInviteCodes,
  getInviteCodeStats,
  deactivateInviteCode,
  validateInviteCode
} from "../services/inviteCodeService";

export const inviteCodeRoutes = new Elysia({ prefix: "/api" })
  .use(requireAuth)

  /**
   * 创建邀请码
   */
  .post("/invite-codes", async ({ body, user }) => {
    const { maxUses, expiresAt, customCode } = body;

    try {
      const inviteCode = await createInviteCode({
        createdBy: user.id,
        maxUses,
        expiresAt: expiresAt ? new Date(expiresAt) : undefined,
        customCode
      });

      return { success: true, inviteCode };
    } catch (error: any) {
      logger.error('创建邀请码失败', { error: error.message, userId: user.id });
      return new Response(JSON.stringify({ error: error.message }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    body: t.Object({
      maxUses: t.Optional(t.Number()),
      expiresAt: t.Optional(t.String()),
      customCode: t.Optional(t.String()),
    }),
    detail: {
      tags: ["邀请码"],
      summary: "创建邀请码",
      description: "创建新的邀请码"
    }
  })

  /**
   * 获取用户的邀请码列表
   */
  .get("/invite-codes", async ({ user }) => {
    try {
      const inviteCodes = await getUserInviteCodes(user.id);
      return { success: true, inviteCodes };
    } catch (error: any) {
      logger.error('获取邀请码列表失败', { error: error.message, userId: user.id });
      return new Response(JSON.stringify({ error: "获取邀请码列表失败" }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    detail: {
      tags: ["邀请码"],
      summary: "获取邀请码列表",
      description: "获取当前用户创建的邀请码列表"
    }
  })

  /**
   * 获取邀请码统计信息
   */
  .get("/invite-codes/stats", async ({ user }) => {
    try {
      const stats = await getInviteCodeStats(user.id);
      return { success: true, stats };
    } catch (error: any) {
      logger.error('获取邀请码统计失败', { error: error.message, userId: user.id });
      return new Response(JSON.stringify({ error: "获取统计信息失败" }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    detail: {
      tags: ["邀请码"],
      summary: "获取邀请码统计",
      description: "获取当前用户的邀请码统计信息"
    }
  })

  /**
   * 禁用邀请码
   */
  .patch("/invite-codes/:code/deactivate", async ({ params, user }) => {
    const { code } = params;

    try {
      const inviteCode = await deactivateInviteCode(code, user.id);
      return { success: true, inviteCode };
    } catch (error: any) {
      logger.error('禁用邀请码失败', { error: error.message, code, userId: user.id });
      return new Response(JSON.stringify({ error: error.message }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    params: t.Object({
      code: t.String(),
    }),
    detail: {
      tags: ["邀请码"],
      summary: "禁用邀请码",
      description: "禁用指定的邀请码"
    }
  })

  /**
   * 管理员：获取所有邀请码
   */
  .use(requireAdmin)
  .get("/admin/invite-codes", async ({ query }) => {
    const { page = 1, limit = 20, search } = query;

    try {
      const skip = (page - 1) * limit;
      
      const where: any = {};
      if (search) {
        where.OR = [
          { code: { contains: search, mode: 'insensitive' } },
          { creator: { username: { contains: search, mode: 'insensitive' } } },
          { creator: { email: { contains: search, mode: 'insensitive' } } }
        ];
      }

      const [inviteCodes, total] = await Promise.all([
        prisma.inviteCode.findMany({
          where,
          include: {
            creator: {
              select: {
                id: true,
                username: true,
                email: true
              }
            },
            usedByUser: {
              select: {
                id: true,
                username: true,
                email: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          skip,
          take: limit
        }),
        prisma.inviteCode.count({ where })
      ]);

      return {
        success: true,
        inviteCodes,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error: any) {
      logger.error('获取所有邀请码失败', { error: error.message });
      return new Response(JSON.stringify({ error: "获取邀请码列表失败" }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    query: t.Object({
      page: t.Optional(t.Number()),
      limit: t.Optional(t.Number()),
      search: t.Optional(t.String()),
    }),
    detail: {
      tags: ["邀请码管理"],
      summary: "获取所有邀请码",
      description: "管理员获取所有邀请码列表"
    }
  })

  /**
   * 管理员：批量创建邀请码
   */
  .post("/admin/invite-codes/batch", async ({ body, user }) => {
    const { count, maxUses, expiresAt } = body;

    try {
      const inviteCodes = [];
      
      for (let i = 0; i < count; i++) {
        const inviteCode = await createInviteCode({
          createdBy: user.id,
          maxUses,
          expiresAt: expiresAt ? new Date(expiresAt) : undefined
        });
        inviteCodes.push(inviteCode);
      }

      return { success: true, inviteCodes };
    } catch (error: any) {
      logger.error('批量创建邀请码失败', { error: error.message, userId: user.id });
      return new Response(JSON.stringify({ error: error.message }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    body: t.Object({
      count: t.Number(),
      maxUses: t.Optional(t.Number()),
      expiresAt: t.Optional(t.String()),
    }),
    detail: {
      tags: ["邀请码管理"],
      summary: "批量创建邀请码",
      description: "管理员批量创建邀请码"
    }
  });
