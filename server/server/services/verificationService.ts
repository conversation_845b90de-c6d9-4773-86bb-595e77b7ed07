import { prisma } from '../db/prisma';
import { logger } from '../utils/logger';

// 验证码类型
export type VerificationType = 'register' | 'login' | 'reset_password';

// 验证码记录接口
export interface VerificationCode {
  id: string;
  email: string;
  code: string;
  type: VerificationType;
  expiresAt: Date;
  used: boolean;
  createdAt: Date;
}

/**
 * 创建验证码记录
 */
export async function createVerificationCode(
  email: string, 
  code: string, 
  type: VerificationType
): Promise<VerificationCode> {
  try {
    // 设置过期时间（10分钟后）
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000);
    
    // 删除该邮箱的旧验证码
    await prisma.verificationCode.deleteMany({
      where: {
        email,
        type,
        used: false
      }
    });

    // 创建新验证码
    const verificationCode = await prisma.verificationCode.create({
      data: {
        email,
        code,
        type,
        expiresAt,
        used: false
      }
    });

    logger.info('Verification code created', {
      email,
      type,
      expiresAt: expiresAt.toISOString()
    });

    return verificationCode as VerificationCode;
  } catch (error) {
    logger.error('Failed to create verification code', { 
      error: error instanceof Error ? error.message : String(error),
      email,
      type 
    });
    throw new Error('创建验证码失败');
  }
}

/**
 * 检查验证码是否有效（不标记为已使用）
 */
export async function checkCode(
  email: string,
  code: string,
  type: VerificationType
): Promise<boolean> {
  try {
    // 开发模式下的万能验证码
    if (process.env.NODE_ENV === 'development' && code === '123456') {
      logger.info('Development mode: using universal verification code', { email, type });
      return true;
    }

    const verificationCode = await prisma.verificationCode.findFirst({
      where: {
        email,
        code,
        type,
        used: false,
        expiresAt: {
          gt: new Date() // 未过期
        }
      }
    });

    if (!verificationCode) {
      logger.warn('Invalid or expired verification code', { email, type });
      return false;
    }

    logger.info('Verification code checked successfully', { email, type });
    return true;
  } catch (error) {
    logger.error('Failed to check code', {
      error: error instanceof Error ? error.message : String(error),
      email,
      type
    });
    return false;
  }
}

/**
 * 验证验证码并标记为已使用
 */
export async function verifyCode(
  email: string,
  code: string,
  type: VerificationType
): Promise<boolean> {
  try {
    // 开发模式下的万能验证码
    if (process.env.NODE_ENV === 'development' && code === '123456') {
      logger.info('Development mode: using universal verification code', { email, type });
      return true;
    }

    const verificationCode = await prisma.verificationCode.findFirst({
      where: {
        email,
        code,
        type,
        used: false,
        expiresAt: {
          gt: new Date() // 未过期
        }
      }
    });

    if (!verificationCode) {
      logger.warn('Invalid or expired verification code', { email, type });
      return false;
    }

    // 标记验证码为已使用
    await prisma.verificationCode.update({
      where: { id: verificationCode.id },
      data: { used: true }
    });

    logger.info('Verification code verified successfully', { email, type });
    return true;
  } catch (error) {
    logger.error('Failed to verify code', {
      error: error instanceof Error ? error.message : String(error),
      email,
      type
    });
    return false;
  }
}

/**
 * 清理过期的验证码
 */
export async function cleanupExpiredCodes(): Promise<void> {
  try {
    const result = await prisma.verificationCode.deleteMany({
      where: {
        expiresAt: {
          lt: new Date()
        }
      }
    });

    if (result.count > 0) {
      logger.info('Cleaned up expired verification codes', { count: result.count });
    }
  } catch (error) {
    logger.error('Failed to cleanup expired codes', { 
      error: error instanceof Error ? error.message : String(error) 
    });
  }
}

/**
 * 检查邮箱是否可以发送验证码（防止频繁发送）
 */
export async function canSendVerificationCode(
  email: string, 
  type: VerificationType
): Promise<{ canSend: boolean; waitTime?: number }> {
  try {
    // 查找最近的验证码记录
    const recentCode = await prisma.verificationCode.findFirst({
      where: {
        email,
        type
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (!recentCode) {
      return { canSend: true };
    }

    // 检查是否在冷却期内（60秒）
    const cooldownPeriod = 60 * 1000; // 60秒
    const timeSinceLastCode = Date.now() - recentCode.createdAt.getTime();
    
    if (timeSinceLastCode < cooldownPeriod) {
      const waitTime = Math.ceil((cooldownPeriod - timeSinceLastCode) / 1000);
      return { canSend: false, waitTime };
    }

    return { canSend: true };
  } catch (error) {
    logger.error('Failed to check verification code cooldown', { 
      error: error instanceof Error ? error.message : String(error),
      email,
      type 
    });
    // 出错时允许发送，避免阻塞用户
    return { canSend: true };
  }
}

// 定期清理过期验证码（可以通过定时任务调用）
setInterval(cleanupExpiredCodes, 30 * 60 * 1000); // 每30分钟清理一次
