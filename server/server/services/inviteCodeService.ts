import { prisma } from '../db/prisma';
import { logger } from '../utils/logger';
import { ValidationError, NotFoundError } from '../utils/errors';
import type { InviteCode } from '@prisma/client';

// 邀请码创建参数
export interface CreateInviteCodeParams {
  createdBy: string;
  maxUses?: number;
  expiresAt?: Date;
  customCode?: string; // 自定义邀请码
}

// 邀请码验证结果
export interface InviteCodeValidation {
  isValid: boolean;
  inviteCode?: InviteCode;
  error?: string;
}

/**
 * 生成随机邀请码
 */
export function generateInviteCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 创建邀请码
 */
export async function createInviteCode(params: CreateInviteCodeParams): Promise<InviteCode> {
  const { createdBy, maxUses = 1, expiresAt, customCode } = params;

  try {
    // 生成邀请码
    let code = customCode || generateInviteCode();
    
    // 确保邀请码唯一
    let attempts = 0;
    while (attempts < 10) {
      const existing = await prisma.inviteCode.findUnique({
        where: { code }
      });
      
      if (!existing) break;
      
      if (customCode) {
        throw new ValidationError('自定义邀请码已存在');
      }
      
      code = generateInviteCode();
      attempts++;
    }

    if (attempts >= 10) {
      throw new Error('生成唯一邀请码失败');
    }

    const inviteCode = await prisma.inviteCode.create({
      data: {
        code,
        createdBy,
        maxUses,
        expiresAt,
        isActive: true,
        usedCount: 0
      },
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            email: true
          }
        }
      }
    });

    logger.info('Invite code created', {
      code: inviteCode.code,
      createdBy,
      maxUses,
      expiresAt: expiresAt?.toISOString()
    });

    return inviteCode;
  } catch (error) {
    logger.error('Failed to create invite code', {
      error: error instanceof Error ? error.message : String(error),
      createdBy
    });
    throw error;
  }
}

/**
 * 验证邀请码
 */
export async function validateInviteCode(code: string): Promise<InviteCodeValidation> {
  try {
    const inviteCode = await prisma.inviteCode.findUnique({
      where: { code },
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            email: true
          }
        }
      }
    });

    if (!inviteCode) {
      return {
        isValid: false,
        error: '邀请码不存在'
      };
    }

    if (!inviteCode.isActive) {
      return {
        isValid: false,
        error: '邀请码已被禁用'
      };
    }

    if (inviteCode.expiresAt && inviteCode.expiresAt < new Date()) {
      return {
        isValid: false,
        error: '邀请码已过期'
      };
    }

    if (inviteCode.usedCount >= inviteCode.maxUses) {
      return {
        isValid: false,
        error: '邀请码使用次数已达上限'
      };
    }

    return {
      isValid: true,
      inviteCode
    };
  } catch (error) {
    logger.error('Failed to validate invite code', {
      error: error instanceof Error ? error.message : String(error),
      code
    });
    return {
      isValid: false,
      error: '验证邀请码时发生错误'
    };
  }
}

/**
 * 使用邀请码
 */
export async function useInviteCode(code: string, usedBy: string): Promise<InviteCode> {
  try {
    // 先验证邀请码
    const validation = await validateInviteCode(code);
    if (!validation.isValid || !validation.inviteCode) {
      throw new ValidationError(validation.error || '邀请码无效');
    }

    // 检查用户是否已经使用过邀请码注册
    const existingUser = await prisma.user.findUnique({
      where: { id: usedBy }
    });

    if (existingUser && existingUser.invitedBy) {
      throw new ValidationError('用户已经使用过邀请码注册');
    }

    // 更新邀请码使用次数
    const updatedInviteCode = await prisma.inviteCode.update({
      where: { code },
      data: {
        usedCount: {
          increment: 1
        },
        usedBy: validation.inviteCode.usedBy || usedBy, // 如果是第一次使用，记录使用者
        updatedAt: new Date()
      },
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            email: true
          }
        }
      }
    });

    logger.info('Invite code used', {
      code,
      usedBy,
      usedCount: updatedInviteCode.usedCount,
      maxUses: updatedInviteCode.maxUses
    });

    return updatedInviteCode;
  } catch (error) {
    logger.error('Failed to use invite code', {
      error: error instanceof Error ? error.message : String(error),
      code,
      usedBy
    });
    throw error;
  }
}

/**
 * 获取用户的邀请码列表
 */
export async function getUserInviteCodes(userId: string): Promise<InviteCode[]> {
  try {
    const inviteCodes = await prisma.inviteCode.findMany({
      where: {
        createdBy: userId
      },
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return inviteCodes;
  } catch (error) {
    logger.error('Failed to get user invite codes', {
      error: error instanceof Error ? error.message : String(error),
      userId
    });
    throw error;
  }
}

/**
 * 获取邀请码统计信息
 */
export async function getInviteCodeStats(userId: string): Promise<{
  totalCodes: number;
  activeCodes: number;
  totalInvites: number;
  successfulInvites: number;
}> {
  try {
    const codes = await prisma.inviteCode.findMany({
      where: {
        createdBy: userId
      }
    });

    const totalCodes = codes.length;
    const activeCodes = codes.filter(code => 
      code.isActive && 
      (!code.expiresAt || code.expiresAt > new Date()) &&
      code.usedCount < code.maxUses
    ).length;
    
    const totalInvites = codes.reduce((sum, code) => sum + code.usedCount, 0);
    const successfulInvites = await prisma.user.count({
      where: {
        invitedBy: userId
      }
    });

    return {
      totalCodes,
      activeCodes,
      totalInvites,
      successfulInvites
    };
  } catch (error) {
    logger.error('Failed to get invite code stats', {
      error: error instanceof Error ? error.message : String(error),
      userId
    });
    throw error;
  }
}

/**
 * 禁用邀请码
 */
export async function deactivateInviteCode(code: string, userId: string): Promise<InviteCode> {
  try {
    const inviteCode = await prisma.inviteCode.findUnique({
      where: { code }
    });

    if (!inviteCode) {
      throw new NotFoundError('邀请码不存在');
    }

    if (inviteCode.createdBy !== userId) {
      throw new ValidationError('只能禁用自己创建的邀请码');
    }

    const updatedInviteCode = await prisma.inviteCode.update({
      where: { code },
      data: {
        isActive: false,
        updatedAt: new Date()
      },
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            email: true
          }
        }
      }
    });

    logger.info('Invite code deactivated', {
      code,
      userId
    });

    return updatedInviteCode;
  } catch (error) {
    logger.error('Failed to deactivate invite code', {
      error: error instanceof Error ? error.message : String(error),
      code,
      userId
    });
    throw error;
  }
}
