import { Resend } from 'resend';
import { logger } from '../utils/logger';

// 初始化Resend客户端
const resend = new Resend(process.env.RESEND_API_KEY);

// 邮件模板类型
export interface EmailTemplate {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

// 验证码邮件模板
export interface VerificationEmailData {
  email: string;
  code: string;
  type: 'register' | 'login' | 'reset_password';
}

/**
 * 发送邮件的通用函数
 */
export async function sendEmail(template: EmailTemplate): Promise<boolean> {
  try {
    if (!process.env.RESEND_API_KEY) {
      logger.error('Resend API key not configured');
      throw new Error('邮件服务未配置');
    }

    console.log('开始发送邮件到:', template.to);

    // 创建带超时的Promise
    const emailPromise = resend.emails.send({
      from: process.env.FROM_EMAIL || '<EMAIL>', // 使用Resend默认发件人
      to: template.to,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });

    // 设置15秒超时
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Email service timeout')), 15000);
    });

    console.log('调用Resend API发送邮件...');
    const result = await Promise.race([emailPromise, timeoutPromise]) as any;
    console.log('Resend API响应成功:', result);

    if (result.error) {
      logger.error('Failed to send email', { error: result.error });
      console.error('Resend API错误:', result.error);
      return false;
    }

    logger.info('Email sent successfully', {
      to: template.to,
      subject: template.subject,
      messageId: result.data?.id
    });
    console.log('邮件发送成功，消息ID:', result.data?.id);
    return true;
  } catch (error) {
    console.error('邮件服务错误:', error);
    logger.error('Email service error', {
      error: error instanceof Error ? error.message : String(error),
      to: template.to
    });

    // 提供更具体的错误信息
    if (error instanceof Error && error.message.includes('timeout')) {
      throw new Error('邮件服务连接超时，请稍后重试');
    }
    return false;
  }
}

/**
 * 发送验证码邮件
 */
export async function sendVerificationEmail(data: VerificationEmailData): Promise<boolean> {
  const { email, code, type } = data;
  
  let subject: string;
  let title: string;
  let description: string;
  
  switch (type) {
    case 'register':
      subject = '验证您的邮箱 - Vibe Coding';
      title = '欢迎注册 Vibe Coding！';
      description = '请使用以下验证码完成注册：';
      break;
    case 'login':
      subject = '登录验证码 - Vibe Coding';
      title = '登录验证';
      description = '请使用以下验证码完成登录：';
      break;
    case 'reset_password':
      subject = '重置密码验证码 - Vibe Coding';
      title = '重置密码';
      description = '请使用以下验证码重置您的密码：';
      break;
    default:
      subject = '验证码 - Vibe Coding';
      title = '验证码';
      description = '您的验证码：';
  }

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${subject}</title>
      <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center; }
        .header h1 { color: white; margin: 0; font-size: 28px; font-weight: 600; }
        .content { padding: 40px 20px; }
        .code-box { background-color: #f1f5f9; border: 2px dashed #cbd5e1; border-radius: 12px; padding: 30px; text-align: center; margin: 30px 0; }
        .code { font-size: 32px; font-weight: bold; color: #1e293b; letter-spacing: 8px; font-family: 'Courier New', monospace; }
        .footer { background-color: #f8fafc; padding: 20px; text-align: center; color: #64748b; font-size: 14px; }
        .warning { background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 15px; margin: 20px 0; border-radius: 4px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>${title}</h1>
        </div>
        <div class="content">
          <p>你好，</p>
          <p>${description}</p>
          
          <div class="code-box">
            <div class="code">${code}</div>
          </div>
          
          <div class="warning">
            <strong>⚠️ 安全提醒：</strong>
            <ul style="margin: 10px 0; padding-left: 20px;">
              <li>验证码有效期为10分钟</li>
              <li>请勿将验证码分享给他人</li>
              <li>如果您没有请求此验证码，请忽略此邮件</li>
            </ul>
          </div>
          
          <p>如果您有任何问题，请联系我们的支持团队。</p>
          
          <p>祝好，<br>Vibe Coding 团队</p>
        </div>
        <div class="footer">
          <p>此邮件由 Vibe Coding 自动发送，请勿回复。</p>
          <p>© 2024 Vibe Coding. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const text = `
${title}

你好，

${description}

验证码：${code}

安全提醒：
- 验证码有效期为10分钟
- 请勿将验证码分享给他人
- 如果您没有请求此验证码，请忽略此邮件

祝好，
Vibe Coding 团队

此邮件由 Vibe Coding 自动发送，请勿回复。
© 2024 Vibe Coding. All rights reserved.
  `;

  return await sendEmail({
    to: email,
    subject,
    html,
    text,
  });
}

/**
 * 生成6位数字验证码
 */
export function generateVerificationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

/**
 * 发送欢迎邮件
 */
export async function sendWelcomeEmail(email: string, username: string): Promise<boolean> {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>欢迎加入 Vibe Coding！</title>
      <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center; }
        .header h1 { color: white; margin: 0; font-size: 28px; font-weight: 600; }
        .content { padding: 40px 20px; }
        .button { display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: 600; margin: 20px 0; }
        .footer { background-color: #f8fafc; padding: 20px; text-align: center; color: #64748b; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🎉 欢迎加入 Vibe Coding！</h1>
        </div>
        <div class="content">
          <p>你好 ${username}，</p>
          <p>欢迎加入 Vibe Coding AI 开发平台！我们很高兴您选择我们的服务。</p>
          
          <p>在 Vibe Coding，您可以：</p>
          <ul>
            <li>🤖 使用 AI 助手进行智能编程</li>
            <li>🚀 快速创建和部署应用</li>
            <li>💻 享受现代化的开发体验</li>
            <li>🔧 使用强大的容器化开发环境</li>
          </ul>
          
          <div style="text-align: center;">
            <a href="http://localhost:5173/dashboard" class="button">开始使用</a>
          </div>
          
          <p>如果您有任何问题或需要帮助，请随时联系我们的支持团队。</p>
          
          <p>祝您编程愉快！<br>Vibe Coding 团队</p>
        </div>
        <div class="footer">
          <p>此邮件由 Vibe Coding 自动发送，请勿回复。</p>
          <p>© 2024 Vibe Coding. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  return await sendEmail({
    to: email,
    subject: '欢迎加入 Vibe Coding！',
    html,
  });
}
