import { OAuth2Client } from 'google-auth-library';
import { prisma } from '../db/prisma';
import { generateToken } from '../utils/jwt';
import { logger } from '../utils/logger';
import type { User } from '@prisma/client';

// Google OAuth客户端 - 延迟初始化
let client: OAuth2Client | null = null;

function getGoogleClient(): OAuth2Client {
  if (!client && process.env.GOOGLE_CLIENT_ID) {
    try {
      client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);
    } catch (error) {
      logger.error('Failed to create Google OAuth client:', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw new Error('Google OAuth client initialization failed');
    }
  }
  if (!client) {
    throw new Error('Google Client ID not configured');
  }
  return client;
}

// Google用户信息接口
interface GoogleUserInfo {
  sub: string; // Google用户ID
  email: string;
  email_verified: boolean;
  name: string;
  picture?: string;
  given_name?: string;
  family_name?: string;
}

/**
 * 验证Google ID Token
 */
export async function verifyGoogleToken(idToken: string): Promise<GoogleUserInfo> {
  try {
    if (!process.env.GOOGLE_CLIENT_ID) {
      throw new Error('Google Client ID not configured');
    }

    const googleClient = getGoogleClient();

    const ticket = await googleClient.verifyIdToken({
      idToken,
      audience: process.env.GOOGLE_CLIENT_ID,
    });

    const payload = ticket.getPayload();
    if (!payload) {
      throw new Error('Invalid Google token payload');
    }

    return {
      sub: payload.sub,
      email: payload.email || '',
      email_verified: payload.email_verified || false,
      name: payload.name || '',
      picture: payload.picture,
      given_name: payload.given_name,
      family_name: payload.family_name,
    };
  } catch (error) {
    logger.error('Google token verification failed', {
      error: error instanceof Error ? error.message : String(error)
    });
    throw new Error('Google token验证失败');
  }
}

/**
 * 处理Google登录
 */
export async function handleGoogleLogin(idToken: string): Promise<{ user: Omit<User, 'password'>; token: string }> {
  try {
    // 验证Google token
    const googleUser = await verifyGoogleToken(idToken);

    if (!googleUser.email_verified) {
      throw new Error('Google邮箱未验证');
    }

    logger.debug('Google user info', {
      email: googleUser.email,
      sub: googleUser.sub
    });

    // 查找现有用户
    let user = await prisma.user.findFirst({
      where: {
        OR: [
          { email: googleUser.email },
          { googleId: googleUser.sub }
        ]
      }
    });

    if (user) {
      // 用户已存在，更新Google ID和头像（如果需要）
      const updateData: any = {};

      if (!user.googleId) {
        updateData.googleId = googleUser.sub;
      }

      // 总是更新头像，确保获取最新的Google头像
      if (googleUser.picture) {
        updateData.avatar = googleUser.picture;
      }

      if (Object.keys(updateData).length > 0) {
        user = await prisma.user.update({
          where: { id: user.id },
          data: updateData
        });
      }

      logger.debug('Existing user logged in via Google', { userId: user.id });
    } else {
      // 创建新用户
      const username = await generateUniqueUsername(googleUser.email);

      user = await prisma.user.create({
        data: {
          username,
          email: googleUser.email,
          avatar: googleUser.picture,
          googleId: googleUser.sub,
          password: '', // Google用户不需要密码
          role: 'user'
        }
      });

      logger.debug('New user created via Google', { userId: user.id, username });
    }

    // 生成JWT token
    const token = await generateToken(user);

    // 排除密码字段
    const { password: _, ...userWithoutPassword } = user;

    return {
      user: userWithoutPassword,
      token
    };
  } catch (error: any) {
    logger.error('Google login failed', { error: error.message });
    throw error;
  }
}

/**
 * 生成唯一用户名
 */
async function generateUniqueUsername(email: string): Promise<string> {
  // 使用邮箱前缀作为用户名
  let baseUsername = email.split('@')[0];

  // 如果邮箱前缀不合适，使用默认前缀
  if (baseUsername.length < 3 || !/^[a-zA-Z0-9_]+$/.test(baseUsername)) {
    baseUsername = 'user';
  }

  // 检查用户名是否已存在
  let username = baseUsername;
  let counter = 1;

  while (await prisma.user.findUnique({ where: { username } })) {
    username = `${baseUsername}${counter}`;
    counter++;
  }

  return username;
}
