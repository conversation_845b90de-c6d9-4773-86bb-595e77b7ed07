<template>
  <div class="container mx-auto p-4">
    <div class="flex flex-col gap-4 md:flex-row md:items-center md:justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold">邀请码管理</h1>
        <p class="text-muted-foreground">管理系统邀请码，控制用户注册</p>
      </div>
      <Button @click="openInviteCodeModal()" class="gap-2 w-full md:w-auto">
        <Plus class="h-4 w-4" />
        创建邀请码
      </Button>
    </div>

    <Card>
      <CardHeader>
        <div class="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <CardTitle class="text-lg md:text-xl">邀请码列表</CardTitle>
            <CardDescription class="text-sm">管理系统邀请码和使用情况</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <!-- 搜索和筛选 -->
        <InviteCodeFilters
          ref="filtersRef"
          :loading="loading"
          :is-refreshing="isRefreshing"
          :is-filtering="isFiltering"
          @search="handleSearch"
          @filter-change="handleFilterChange"
          @refresh="refreshInviteCodes"
        />

        <!-- 邀请码表格 -->
        <InviteCodeTable
          :invite-codes="inviteCodes"
          :loading="loading"
          :error="error"
          :filter-applied="filterApplied"
          @edit-invite-code="openInviteCodeModal"
          @delete-invite-code="confirmDeleteInviteCode"
          @deactivate-invite-code="confirmDeactivateInviteCode"
          @add-invite-code="openInviteCodeModal"
          @reset-filters="resetFilters"
          @retry="fetchInviteCodes"
        />

        <!-- 分页控件 -->
        <div class="border-t">
          <PaginationControls
            :pagination="pagination"
            :filter-applied="filterApplied"
            @go-to-page="goToPage"
            @change-page-size="changePageSize"
            @reset-filters="resetFilters"
          />
        </div>
      </CardContent>
    </Card>

    <!-- 邀请码创建/编辑模态框 -->
    <InviteCodeModal
      v-model:is-open="isInviteCodeModalOpen"
      :invite-code="currentInviteCode"
      :saving="saving"
      :error="modalError"
      @save="saveInviteCode"
    />

    <!-- 删除确认模态框 -->
    <DeleteConfirmModal
      v-model:is-open="isDeleteModalOpen"
      :deleting="deleting"
      :error="deleteError"
      :item-name="inviteCodeToDelete?.code || ''"
      item-type="邀请码"
      @confirm="deleteInviteCode"
    />

    <!-- 停用确认模态框 -->
    <DeactivateConfirmModal
      v-model:is-open="isDeactivateModalOpen"
      :deactivating="deactivating"
      :error="deactivateError"
      :item-name="inviteCodeToDeactivate?.code || ''"
      @confirm="deactivateInviteCode"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '~/components/ui/card'
import { Plus } from 'lucide-vue-next'

// 导入组件
import InviteCodeFilters from '~/components/inviteCode/InviteCodeFilters.vue'
import InviteCodeTable from '~/components/inviteCode/InviteCodeTable.vue'
import PaginationControls from '~/components/inviteCode/PaginationControls.vue'
import InviteCodeModal from '~/components/inviteCode/InviteCodeModal.vue'
import DeleteConfirmModal from '~/components/inviteCode/DeleteConfirmModal.vue'
import DeactivateConfirmModal from '~/components/inviteCode/DeactivateConfirmModal.vue'

// 导入邀请码类型和API
import { useInviteCodeApi } from '~/composables/useInviteCodeApi'
import type { InviteCode, InviteCodeListParams } from '~/composables/useInviteCodeApi'

const inviteCodeApi = useInviteCodeApi()

// 响应式数据
const inviteCodes = ref<InviteCode[]>([])
const loading = ref(false)
const error = ref<string | null>(null)
const isRefreshing = ref(false)
const isFiltering = ref(false)

// 分页数据
const pagination = ref({
  page: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0
})

// 搜索和筛选
const searchQuery = ref('')
const statusFilter = ref<string>('')
const filterApplied = ref(false)

// 模态框状态
const isInviteCodeModalOpen = ref(false)
const currentInviteCode = ref<InviteCode | null>(null)
const saving = ref(false)
const modalError = ref<string | null>(null)

// 删除相关
const isDeleteModalOpen = ref(false)
const inviteCodeToDelete = ref<InviteCode | null>(null)
const deleting = ref(false)
const deleteError = ref<string | null>(null)

// 停用相关
const isDeactivateModalOpen = ref(false)
const inviteCodeToDeactivate = ref<InviteCode | null>(null)
const deactivating = ref(false)
const deactivateError = ref<string | null>(null)

// 引用
const filtersRef = ref()

// 获取邀请码列表
const fetchInviteCodes = async (params?: Partial<InviteCodeListParams>) => {
  loading.value = true
  error.value = null

  try {
    const queryParams: InviteCodeListParams = {
      page: pagination.value.page,
      pageSize: pagination.value.pageSize,
      search: searchQuery.value,
      status: statusFilter.value,
      ...params
    }

    const response = await inviteCodeApi.getInviteCodesPaginated(queryParams)
    
    if (response.success && response.data) {
      inviteCodes.value = response.data.inviteCodes
      pagination.value = {
        page: response.data.pagination.page,
        pageSize: response.data.pagination.pageSize,
        total: response.data.pagination.total,
        totalPages: response.data.pagination.totalPages
      }
    } else {
      throw new Error(response.message || '获取邀请码列表失败')
    }
  } catch (e: any) {
    error.value = e.message || '获取邀请码列表失败'
    console.error('获取邀请码列表失败:', e)
  } finally {
    loading.value = false
    isRefreshing.value = false
    isFiltering.value = false
  }
}

// 刷新邀请码列表
const refreshInviteCodes = async () => {
  isRefreshing.value = true
  await fetchInviteCodes()
}

// 搜索处理
const handleSearch = async (query: string) => {
  searchQuery.value = query
  pagination.value.page = 1
  filterApplied.value = !!(query || statusFilter.value)
  isFiltering.value = true
  await fetchInviteCodes()
}

// 筛选处理
const handleFilterChange = async (filters: { status: string }) => {
  statusFilter.value = filters.status
  pagination.value.page = 1
  filterApplied.value = !!(searchQuery.value || filters.status)
  isFiltering.value = true
  await fetchInviteCodes()
}

// 重置筛选
const resetFilters = async () => {
  searchQuery.value = ''
  statusFilter.value = ''
  pagination.value.page = 1
  filterApplied.value = false
  
  // 重置筛选组件
  if (filtersRef.value?.resetFilters) {
    filtersRef.value.resetFilters()
  }
  
  await fetchInviteCodes()
}

// 分页处理
const goToPage = async (page: number) => {
  pagination.value.page = page
  await fetchInviteCodes()
}

const changePageSize = async (pageSize: number) => {
  pagination.value.pageSize = pageSize
  pagination.value.page = 1
  await fetchInviteCodes()
}

// 打开邀请码模态框
const openInviteCodeModal = (inviteCode?: InviteCode) => {
  currentInviteCode.value = inviteCode || null
  modalError.value = null
  isInviteCodeModalOpen.value = true
}

// 保存邀请码
const saveInviteCode = async (data: any) => {
  saving.value = true
  modalError.value = null

  try {
    if (data.id) {
      // 更新邀请码（目前只支持停用/激活）
      await inviteCodeApi.updateInviteCode(data.id, { isActive: data.isActive })
      
      const message = useMessage()
      message.success('更新成功', `邀请码 "${data.code}" 已更新`)
    } else {
      // 创建邀请码
      await inviteCodeApi.createInviteCode(data)
      
      const message = useMessage()
      message.success('创建成功', `邀请码已创建`)
    }

    isInviteCodeModalOpen.value = false
    refreshInviteCodes()
  } catch (e: any) {
    modalError.value = e.message || '保存邀请码失败'
    
    const message = useMessage()
    message.error('操作失败', e.message || '保存邀请码失败')
  } finally {
    saving.value = false
  }
}

// 确认删除邀请码
const confirmDeleteInviteCode = (inviteCode: InviteCode) => {
  inviteCodeToDelete.value = inviteCode
  deleteError.value = null
  isDeleteModalOpen.value = true
}

// 删除邀请码
const deleteInviteCode = async () => {
  if (!inviteCodeToDelete.value) return

  deleting.value = true
  deleteError.value = null

  try {
    const codeValue = inviteCodeToDelete.value.code
    await inviteCodeApi.deleteInviteCode(inviteCodeToDelete.value.id)

    const message = useMessage()
    message.success('删除成功', `邀请码 "${codeValue}" 已删除`)

    isDeleteModalOpen.value = false
    refreshInviteCodes()
  } catch (e: any) {
    deleteError.value = e.message || '删除邀请码失败'
    
    const message = useMessage()
    message.error('删除失败', e.message || '删除邀请码失败')
  } finally {
    deleting.value = false
  }
}

// 确认停用邀请码
const confirmDeactivateInviteCode = (inviteCode: InviteCode) => {
  inviteCodeToDeactivate.value = inviteCode
  deactivateError.value = null
  isDeactivateModalOpen.value = true
}

// 停用邀请码
const deactivateInviteCode = async () => {
  if (!inviteCodeToDeactivate.value) return

  deactivating.value = true
  deactivateError.value = null

  try {
    const codeValue = inviteCodeToDeactivate.value.code
    await inviteCodeApi.deactivateInviteCode(inviteCodeToDeactivate.value.id)

    const message = useMessage()
    message.success('停用成功', `邀请码 "${codeValue}" 已停用`)

    isDeactivateModalOpen.value = false
    refreshInviteCodes()
  } catch (e: any) {
    deactivateError.value = e.message || '停用邀请码失败'
    
    const message = useMessage()
    message.error('停用失败', e.message || '停用邀请码失败')
  } finally {
    deactivating.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchInviteCodes()
})

// 添加登录验证
definePageMeta({
  middleware: ['auth']
})
</script>
