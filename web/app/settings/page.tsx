'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save, User as UserIcon, Mail, Lock, Eye, EyeOff, AlertCircle } from 'lucide-react';
import { useAuth } from '../../hooks/use-auth';
import { userApi, authApi } from '../../lib/api';
import type { User } from '../../types';

interface SettingsFormData {
  username: string;
  email: string;
  emailVerificationCode: string;
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface FormErrors {
  [key: string]: string;
}

export default function SettingsPage() {
  const router = useRouter();
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const [mounted, setMounted] = useState(false);
  
  const [formData, setFormData] = useState<SettingsFormData>({
    username: '',
    email: '',
    emailVerificationCode: '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [activeTab, setActiveTab] = useState<'profile' | 'password'>('profile');
  const [emailChanged, setEmailChanged] = useState(false);
  const [emailCodeSent, setEmailCodeSent] = useState(false);
  const [emailCountdown, setEmailCountdown] = useState(0);

  // 客户端挂载状态
  useEffect(() => {
    setMounted(true);
  }, []);

  // 如果未登录，重定向到登录页
  useEffect(() => {
    if (mounted && !authLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [mounted, isAuthenticated, authLoading, router]);

  // 初始化表单数据
  useEffect(() => {
    if (user) {
      setFormData(prev => ({
        ...prev,
        username: user.username || '',
        email: user.email || '',
      }));
      // 重置邮箱改变状态
      setEmailChanged(false);
      setEmailCodeSent(false);
    }
  }, [user]);

  // 邮箱验证码倒计时
  useEffect(() => {
    if (emailCountdown > 0) {
      const timer = setTimeout(() => {
        setEmailCountdown(emailCountdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [emailCountdown]);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // 检查邮箱是否改变
    if (name === 'email' && user && value !== user.email) {
      setEmailChanged(true);
      setEmailCodeSent(false);
      setFormData(prev => ({ ...prev, emailVerificationCode: '' }));
    } else if (name === 'email' && user && value === user.email) {
      setEmailChanged(false);
    }

    // 清除对应字段的错误
    if (formErrors[name]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // 发送邮箱验证码
  const handleSendEmailCode = async () => {
    if (!formData.email.trim()) {
      setFormErrors({ email: '请输入邮箱地址' });
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setFormErrors({ email: '请输入有效的邮箱地址' });
      return;
    }

    try {
      await authApi.sendVerificationCode(formData.email, 'register');
      setEmailCodeSent(true);
      setEmailCountdown(60);
      setFormErrors({});
    } catch (error: any) {
      setFormErrors({ email: error.message || '发送验证码失败' });
    }
  };

  // 验证表单
  const validateForm = (tab: 'profile' | 'password'): boolean => {
    const errors: FormErrors = {};

    if (tab === 'profile') {
      // 用户名验证
      if (!formData.username.trim()) {
        errors.username = '用户名不能为空';
      } else if (formData.username.length < 3) {
        errors.username = '用户名至少需要3个字符';
      }

      // 邮箱验证
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (formData.email && !emailRegex.test(formData.email)) {
        errors.email = '请输入有效的邮箱地址';
      }

      // 如果邮箱改变了，需要验证码
      if (emailChanged && !formData.emailVerificationCode) {
        errors.emailVerificationCode = '请输入邮箱验证码';
      } else if (formData.emailVerificationCode && formData.emailVerificationCode.length !== 6) {
        errors.emailVerificationCode = '验证码必须是6位数字';
      }
    } else if (tab === 'password') {
      // 密码修改验证
      if (!formData.currentPassword) {
        errors.currentPassword = '请输入当前密码';
      }
      
      if (!formData.newPassword) {
        errors.newPassword = '请输入新密码';
      } else if (formData.newPassword.length < 6) {
        errors.newPassword = '新密码至少需要6个字符';
      }
      
      if (formData.newPassword !== formData.confirmPassword) {
        errors.confirmPassword = '两次输入的密码不一致';
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // 保存个人资料
  const handleSaveProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm('profile') || !user) {
      return;
    }

    setLoading(true);
    
    try {
      const updateData: any = {
        username: formData.username.trim(),
      };

      // 如果邮箱改变了，添加邮箱和验证码
      if (emailChanged) {
        updateData.email = formData.email.trim();
        updateData.emailVerificationCode = formData.emailVerificationCode;
      }

      await userApi.updateUser(user.id, updateData);
      
      // 这里应该更新本地用户状态，但需要重新获取用户信息
      alert('个人资料更新成功！');
    } catch (error: any) {
      setFormErrors({ general: error.message || '更新失败' });
    } finally {
      setLoading(false);
    }
  };

  // 修改密码
  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm('password') || !user) {
      return;
    }

    setLoading(true);
    
    try {
      // 验证当前密码并更新新密码
      await userApi.updateUser(user.id, {
        password: formData.newPassword,
        currentPassword: formData.currentPassword,
      });
      
      // 清空密码字段
      setFormData(prev => ({
        ...prev,
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      }));
      
      alert('密码修改成功！');
    } catch (error: any) {
      setFormErrors({ general: error.message || '密码修改失败' });
    } finally {
      setLoading(false);
    }
  };

  if (!mounted || authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="w-8 h-8 border-2 border-slate-300 border-t-slate-600 rounded-full animate-spin"></div>
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-6 py-4">
          <button
            onClick={() => router.back()}
            className="inline-flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>返回</span>
          </button>
        </div>
      </nav>

      <div className="container mx-auto px-6 py-8">
        <div className="max-w-2xl mx-auto">
          <h1 className="text-2xl font-bold text-gray-900 mb-8">账户设置</h1>

          {/* 标签页 */}
          <div className="border-b border-gray-200 mb-8">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('profile')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'profile'
                    ? 'border-slate-500 text-slate-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                个人资料
              </button>
              <button
                onClick={() => setActiveTab('password')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'password'
                    ? 'border-slate-500 text-slate-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                修改密码
              </button>
            </nav>
          </div>

          {/* 错误提示 */}
          {formErrors.general && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-5 h-5 text-red-500" />
                <p className="text-sm text-red-600">{formErrors.general}</p>
              </div>
            </div>
          )}

          {/* 个人资料标签页 */}
          {activeTab === 'profile' && (
            <form onSubmit={handleSaveProfile} className="space-y-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-6">基本信息</h2>
                
                <div className="space-y-4">
                  {/* 用户名 */}
                  <div>
                    <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-2">
                      用户名
                    </label>
                    <div className="relative">
                      <UserIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="text"
                        id="username"
                        name="username"
                        value={formData.username}
                        onChange={handleInputChange}
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors ${
                          formErrors.username ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                        placeholder="请输入用户名"
                        disabled={loading}
                      />
                    </div>
                    {formErrors.username && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.username}</p>
                    )}
                  </div>

                  {/* 邮箱 */}
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                      邮箱地址 {emailChanged && <span className="text-orange-500">(已修改，需要验证)</span>}
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors ${
                          formErrors.email ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                        placeholder="请输入邮箱地址"
                        disabled={loading}
                      />
                    </div>
                    {/* 邮箱改变时显示发送验证码按钮 */}
                    {emailChanged && (
                      <div className="mt-2">
                        <button
                          type="button"
                          onClick={handleSendEmailCode}
                          disabled={loading || emailCountdown > 0}
                          className="text-sm text-slate-600 hover:text-slate-800 disabled:text-slate-400"
                        >
                          {emailCountdown > 0 ? `${emailCountdown}秒后可重发` : '发送验证码'}
                        </button>
                      </div>
                    )}

                    {formErrors.email && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.email}</p>
                    )}
                  </div>

                  {/* 邮箱验证码输入 */}
                  {emailChanged && emailCodeSent && (
                    <div>
                      <label htmlFor="emailVerificationCode" className="block text-sm font-medium text-gray-700 mb-2">
                        邮箱验证码
                      </label>
                      <input
                        type="text"
                        id="emailVerificationCode"
                        name="emailVerificationCode"
                        value={formData.emailVerificationCode}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors ${
                          formErrors.emailVerificationCode ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                        placeholder="请输入6位验证码"
                        maxLength={6}
                        disabled={loading}
                      />
                      {formErrors.emailVerificationCode && (
                        <p className="mt-1 text-sm text-red-600">{formErrors.emailVerificationCode}</p>
                      )}
                    </div>
                  )}
                </div>

                <div className="mt-6 flex justify-end">
                  <button
                    type="submit"
                    disabled={loading}
                    className="inline-flex items-center px-4 py-2 bg-slate-900 hover:bg-slate-800 disabled:bg-slate-400 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors"
                  >
                    {loading ? (
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                    ) : (
                      <Save className="w-4 h-4 mr-2" />
                    )}
                    保存更改
                  </button>
                </div>
              </div>
            </form>
          )}

          {/* 修改密码标签页 */}
          {activeTab === 'password' && (
            <form onSubmit={handleChangePassword} className="space-y-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-6">修改密码</h2>
                
                <div className="space-y-4">
                  {/* 当前密码 */}
                  <div>
                    <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700 mb-2">
                      当前密码
                    </label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type={showCurrentPassword ? 'text' : 'password'}
                        id="currentPassword"
                        name="currentPassword"
                        value={formData.currentPassword}
                        onChange={handleInputChange}
                        className={`w-full pl-10 pr-12 py-3 border rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors ${
                          formErrors.currentPassword ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                        placeholder="请输入当前密码"
                        disabled={loading}
                      />
                      <button
                        type="button"
                        onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        disabled={loading}
                      >
                        {showCurrentPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </button>
                    </div>
                    {formErrors.currentPassword && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.currentPassword}</p>
                    )}
                  </div>

                  {/* 新密码 */}
                  <div>
                    <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 mb-2">
                      新密码
                    </label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type={showNewPassword ? 'text' : 'password'}
                        id="newPassword"
                        name="newPassword"
                        value={formData.newPassword}
                        onChange={handleInputChange}
                        className={`w-full pl-10 pr-12 py-3 border rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors ${
                          formErrors.newPassword ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                        placeholder="请输入新密码"
                        disabled={loading}
                      />
                      <button
                        type="button"
                        onClick={() => setShowNewPassword(!showNewPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        disabled={loading}
                      >
                        {showNewPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </button>
                    </div>
                    {formErrors.newPassword && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.newPassword}</p>
                    )}
                  </div>

                  {/* 确认新密码 */}
                  <div>
                    <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
                      确认新密码
                    </label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type={showConfirmPassword ? 'text' : 'password'}
                        id="confirmPassword"
                        name="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleInputChange}
                        className={`w-full pl-10 pr-12 py-3 border rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors ${
                          formErrors.confirmPassword ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                        placeholder="请再次输入新密码"
                        disabled={loading}
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        disabled={loading}
                      >
                        {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </button>
                    </div>
                    {formErrors.confirmPassword && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.confirmPassword}</p>
                    )}
                  </div>
                </div>

                <div className="mt-6 flex justify-end">
                  <button
                    type="submit"
                    disabled={loading}
                    className="inline-flex items-center px-4 py-2 bg-slate-900 hover:bg-slate-800 disabled:bg-slate-400 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors"
                  >
                    {loading ? (
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                    ) : (
                      <Save className="w-4 h-4 mr-2" />
                    )}
                    修改密码
                  </button>
                </div>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}
