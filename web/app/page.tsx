'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowR<PERSON>, Sparkles, Settings, ChevronDown, User, LogOut, BookOpen, FolderOpen } from 'lucide-react';
import { useAuth } from '../hooks/use-auth';
import ProjectListModal from '../components/project-list-modal';

export default function HomePage() {
  const router = useRouter();
  const { isAuthenticated, user, logout } = useAuth();
  const [projectName, setProjectName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showFrameworkDropdown, setShowFrameworkDropdown] = useState(false);
  const [showProjectListModal, setShowProjectListModal] = useState(false);
  const [mounted, setMounted] = useState(false);
  const userMenuRef = useRef<HTMLDivElement>(null);

  // 防止水合错误
  useEffect(() => {
    setMounted(true);
  }, []);

  const handleCreateProject = async () => {
    if (!projectName.trim()) return;

    if (!isAuthenticated) {
      router.push('/login');
      return;
    }

    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));

      const generateProjectName = (description: string): string => {
        const desc = description.trim();
        if (desc.includes('博客')) return '我的博客系统';
        if (desc.includes('电商') || desc.includes('购物')) return '电商购物网站';
        if (desc.includes('任务') || desc.includes('管理')) return '任务管理应用';
        const words = desc.split(/\s+/).slice(0, 3);
        return words.length > 0 ? `${words.join('')}应用` : '我的应用';
      };

      const newProject = {
        id: Date.now().toString(),
        name: generateProjectName(projectName),
        description: projectName.trim(),
        template: 'Next.js',
        status: 'building' as const,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        tags: ['AI生成', 'nextjs']
      };

      try {
        const existingProjects = JSON.parse(localStorage.getItem('xcoding_projects') || '[]');
        existingProjects.unshift(newProject);
        localStorage.setItem('xcoding_projects', JSON.stringify(existingProjects));
      } catch (storageError) {
        console.warn('Failed to save to localStorage:', storageError);
      }

      router.push(`/workspace/${newProject.id}`);
    } catch (error) {
      console.error('Error creating project:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleCreateProject();
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    };

    if (showUserMenu) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [showUserMenu]);

  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-slate-50"></div>
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-blue-100/30 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-to-tl from-slate-100/40 to-transparent rounded-full blur-3xl"></div>
      </div>

      {/* 网格背景 */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#f1f5f9_1px,transparent_1px),linear-gradient(to_bottom,#f1f5f9_1px,transparent_1px)] bg-[size:4rem_4rem] opacity-20"></div>

      {/* 导航栏 */}
      <nav className="relative z-20 flex justify-between items-center p-6">
        <div className="flex items-center space-x-6">
          <Link
            href="/blog"
            className="flex items-center space-x-2 text-slate-600 hover:text-slate-900 transition-colors"
          >
            <BookOpen className="w-4 h-4" />
            <span className="text-sm font-medium">博客</span>
          </Link>
        </div>

        <div className="flex items-center space-x-4">
          {!mounted ? (
            // 水合前显示占位符，避免布局跳动
            <div className="flex items-center space-x-3">
              <div className="px-3 py-2 text-sm text-slate-600 font-medium rounded-lg">
                登录
              </div>
              <div className="px-3 py-2 bg-slate-900 text-white text-sm font-medium rounded-lg">
                开始使用
              </div>
            </div>
          ) : isAuthenticated ? (
            <>
              {/* 我的项目按钮 */}
              <button
                onClick={() => setShowProjectListModal(true)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors"
              >
                <FolderOpen className="w-4 h-4" />
                <span className="font-medium">我的项目</span>
              </button>
            <div className="relative" ref={userMenuRef}>
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                onMouseEnter={() => setShowUserMenu(true)}
                className="flex items-center space-x-2 text-slate-600 hover:text-slate-900 transition-colors"
              >
                {user?.avatar ? (
                  <img
                    src={user.avatar}
                    alt={user.username}
                    className="w-8 h-8 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-8 h-8 bg-slate-900 rounded-full flex items-center justify-center">
                    <User className="w-4 h-4 text-white" />
                  </div>
                )}
                <span className="text-sm font-medium">{ user?.username}</span>
                <ChevronDown className="w-4 h-4" />
              </button>

              {showUserMenu && (
                <div
                  className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-slate-200 py-1 z-50"
                  onMouseLeave={() => setShowUserMenu(false)}
                >
                  <div className="px-4 py-2 border-b border-slate-200">
                    <p className="text-sm font-medium text-slate-900">{ user?.username}</p>
                    <p className="text-xs text-slate-500">{user?.email || '未设置邮箱'}</p>
                  </div>

                  <button
                    onClick={() => {
                      router.push('/settings');
                      setShowUserMenu(false);
                    }}
                    className="w-full px-4 py-2 text-left text-sm text-slate-700 hover:bg-slate-50 flex items-center space-x-2"
                  >
                    <Settings className="w-4 h-4" />
                    <span>账户设置</span>
                  </button>

                  <button
                    onClick={() => {
                      logout();
                      setShowUserMenu(false);
                    }}
                    className="w-full px-4 py-2 text-left text-sm text-slate-700 hover:bg-slate-50 flex items-center space-x-2"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>退出登录</span>
                  </button>
                </div>
              )}
            </div>
            </>
          ) : (
            <>
              <Link
                href="/login"
                className="px-3 py-2 text-sm text-slate-600 hover:text-slate-900 font-medium transition-colors rounded-lg hover:bg-slate-50"
              >
                登录
              </Link>
              <Link
                href="/register"
                className="px-3 py-2 bg-slate-900 hover:bg-slate-800 text-white text-sm font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
              >
                开始使用
              </Link>
            </>
          )}
        </div>
      </nav>

      {/* 主要内容区域 */}
      <main className="relative z-10 flex-1 flex flex-col items-center px-6 pt-16 pb-16">
        <div className="max-w-3xl w-full text-center">
          {/* 品牌区域 */}
          <div className="mb-12">
            <div className="flex items-center justify-center space-x-4 mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-slate-900 to-slate-700 rounded-2xl flex items-center justify-center shadow-lg">
                <Sparkles className="w-7 h-7 text-white" />
              </div>
              <h1 className="text-3xl font-bold text-slate-900 tracking-tight">
                XCoding
              </h1>
            </div>

            <p className="text-lg text-slate-600 font-medium mb-8">
              {!mounted
                ? '登录后开始创建你的AI应用'
                : isAuthenticated
                ? '描述你想要构建的应用'
                : '登录后开始创建你的AI应用'
              }
            </p>
          </div>

          {/* 输入区域 */}
          <div className="relative mb-8">
            <div className="relative bg-white rounded-2xl border border-slate-200 transition-all duration-200 shadow-lg hover:shadow-xl">
              <div className="p-4">
                <div className="relative">
                  <textarea
                    value={projectName}
                    onChange={(e) => setProjectName(e.target.value)}
                    onKeyDown={handleKeyPress}
                    placeholder="描述你想要创建的应用...&#10;例如：一个现代化的博客系统，支持用户注册、文章发布和评论功能"
                    className="w-full px-4 py-3 pr-12 text-lg text-slate-900 placeholder-slate-500 bg-transparent border-none outline-none font-medium resize-none"
                    rows={3}
                    disabled={isLoading}
                    style={{ minHeight: '80px' }}
                  />
                  <button
                    onClick={handleCreateProject}
                    disabled={!projectName.trim() || isLoading}
                    className="absolute bottom-2 right-2 p-2 bg-slate-900 hover:bg-slate-800 disabled:bg-slate-300 disabled:cursor-not-allowed text-white rounded-lg flex items-center justify-center transition-colors"
                  >
                    {isLoading ? (
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    ) : (
                      <ArrowRight className="w-4 h-4" />
                    )}
                  </button>
                </div>
              </div>

              {/* 底部工具栏 */}
              <div className="flex items-center justify-between px-4 pb-3 pt-2 border-t border-slate-100">
                <div className="flex items-center space-x-3">
                  {/* 框架选择下拉菜单 */}
                  <div className="relative">
                    <button
                      onClick={() => setShowFrameworkDropdown(!showFrameworkDropdown)}
                      className="flex items-center space-x-2 px-3 py-1.5 text-sm font-medium text-slate-600 hover:text-slate-900 hover:bg-slate-50 rounded-lg transition-colors"
                    >
                      <Settings className="w-4 h-4" />
                      <span>Next.js</span>
                      <ChevronDown className="w-3 h-3" />
                    </button>

                    {showFrameworkDropdown && (
                      <div className="absolute bottom-full left-0 mb-2 w-80 bg-white rounded-lg shadow-lg border border-slate-200 z-20 max-h-96 overflow-hidden">
                        <div className="max-h-80 overflow-y-auto">
                          {/* Next.js - 当前可用 */}
                          <button
                            onClick={() => {
                              setShowFrameworkDropdown(false);
                            }}
                            className="w-full text-left px-4 py-3 hover:bg-slate-50 first:rounded-t-lg"
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="text-sm font-medium text-slate-900 mb-1">
                                  Next.js
                                </div>
                                <div className="text-xs text-slate-500 leading-relaxed">
                                  全栈前后端 + PostgreSQL，可以构建完整的Web应用
                                </div>
                              </div>
                              <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full ml-3 flex-shrink-0">
                                可用
                              </span>
                            </div>
                          </button>

                          {/* Vue 3 - 即将推出 */}
                          <button
                            className="w-full text-left px-4 py-3 hover:bg-slate-50 opacity-50 cursor-not-allowed"
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="text-sm font-medium text-slate-900 mb-1">
                                  Vue 3
                                </div>
                                <div className="text-xs text-slate-500 leading-relaxed">
                                  基于 Vue 3 + TypeScript 的现代化前端框架
                                </div>
                              </div>
                              <span className="text-xs text-amber-600 bg-amber-100 px-2 py-1 rounded-full ml-3 flex-shrink-0">
                                即将推出
                              </span>
                            </div>
                          </button>

                          {/* React - 即将推出 */}
                          <button
                            className="w-full text-left px-4 py-3 hover:bg-slate-50 opacity-50 cursor-not-allowed"
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="text-sm font-medium text-slate-900 mb-1">
                                  React
                                </div>
                                <div className="text-xs text-slate-500 leading-relaxed">
                                  使用 React + TypeScript 构建单页应用
                                </div>
                              </div>
                              <span className="text-xs text-amber-600 bg-amber-100 px-2 py-1 rounded-full ml-3 flex-shrink-0">
                                即将推出
                              </span>
                            </div>
                          </button>

                          {/* Svelte - 即将推出 */}
                          <button
                            className="w-full text-left px-4 py-3 hover:bg-slate-50 opacity-50 cursor-not-allowed last:rounded-b-lg"
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="text-sm font-medium text-slate-900 mb-1">
                                  Svelte
                                </div>
                                <div className="text-xs text-slate-500 leading-relaxed">
                                  轻量级的现代前端框架，编译时优化
                                </div>
                              </div>
                              <span className="text-xs text-amber-600 bg-amber-100 px-2 py-1 rounded-full ml-3 flex-shrink-0">
                                即将推出
                              </span>
                            </div>
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                <div className="text-sm text-slate-400 font-medium">
                  XCoding AI
                </div>
              </div>
            </div>
          </div>

          {/* 底部信息 */}
          <div className="text-center">
            <p className="text-slate-500 text-sm mb-4">
              由 AI 驱动的智能开发环境
            </p>
            <div className="flex flex-wrap justify-center items-center gap-3 text-xs text-slate-400">
              <span>支持 Next.js</span>
              <span>•</span>
              <span>云端部署</span>
              <span>•</span>
              <span>AI 代码助手</span>
              <span>•</span>
              <span>实时预览</span>
            </div>
          </div>
        </div>
      </main>

      {/* 页脚 */}
      <footer className="relative z-10 py-8">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 sm:gap-0 text-sm text-slate-500">
            <div className="text-center sm:text-left">
              © 2025 XCoding. All rights reserved.
            </div>
            <div className="flex space-x-6">
              <a href="#" className="hover:text-slate-900 transition-colors">帮助</a>
              <a href="#" className="hover:text-slate-900 transition-colors">隐私政策</a>
              <a href="#" className="hover:text-slate-900 transition-colors">服务条款</a>
            </div>
          </div>
        </div>
      </footer>

      {/* 项目列表弹窗 */}
      <ProjectListModal
        open={showProjectListModal}
        onOpenChange={setShowProjectListModal}
      />
    </div>
  );
}
