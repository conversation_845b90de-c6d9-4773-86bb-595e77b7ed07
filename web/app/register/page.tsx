'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Eye, EyeOff, Sparkles, AlertCircle, CheckCircle } from 'lucide-react';
import { useAuth } from '../../hooks/use-auth';
import { validators } from '../../lib/utils';
import { authApi } from '../../lib/api';
import GoogleLoginButton from '../../components/google-login-button';
import type { RegisterData, FormErrors } from '../../types';

export default function RegisterPage() {
  const router = useRouter();
  const { register, sendVerificationCode, loginWithGoogle, isAuthenticated, loading, error, clearError } = useAuth();

  const [formData, setFormData] = useState<RegisterData>({
    email: '',
    password: '',
    verificationCode: '',
    username: '',
    confirmPassword: '',
    inviteCode: '',
  });

  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState<{ valid: boolean; message: string }>({
    valid: false,
    message: '',
  });

  // 邮箱验证码相关状态
  const [step, setStep] = useState<'invite' | 'email' | 'verification' | 'password'>('invite');
  const [countdown, setCountdown] = useState(0);
  const [isCodeSent, setIsCodeSent] = useState(false);
  const [verifyLoading, setVerifyLoading] = useState(false);
  const [inviteCodeValid, setInviteCodeValid] = useState(false);
  const [inviteCodeInfo, setInviteCodeInfo] = useState<any>(null);

  // 如果已经登录，重定向到首页
  useEffect(() => {
    if (isAuthenticated) {
      router.push('/');
    }
  }, [isAuthenticated, router]);

  // 清除错误信息
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        clearError();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  // 监听密码变化，实时检查强度
  useEffect(() => {
    if (formData.password) {
      const strength = validators.password(formData.password);
      setPasswordStrength(strength);
    } else {
      setPasswordStrength({ valid: false, message: '' });
    }
  }, [formData.password]);

  // 倒计时效果
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // 清除对应字段的错误
    if (formErrors[name as keyof FormErrors]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // 验证邀请码
  const handleValidateInviteCode = async () => {
    if (!formData.inviteCode.trim()) {
      setFormErrors({ inviteCode: '请输入邀请码' });
      return;
    }

    setVerifyLoading(true);
    try {
      const response = await authApi.validateInviteCode(formData.inviteCode);
      setInviteCodeValid(true);
      setInviteCodeInfo(response.inviteCode);
      setFormErrors({});
      setStep('email');
    } catch (error: any) {
      setFormErrors({ inviteCode: error.message || '邀请码无效' });
    } finally {
      setVerifyLoading(false);
    }
  };

  // 发送验证码
  const handleSendCode = async () => {
    try {
      // 验证邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!formData.email.trim()) {
        setFormErrors({ email: '请输入邮箱地址' });
        return;
      }
      if (!emailRegex.test(formData.email)) {
        setFormErrors({ email: '请输入有效的邮箱地址' });
        return;
      }

      console.log('发送验证码到:', formData.email);
      await sendVerificationCode(formData.email, 'register');
      setIsCodeSent(true);
      setCountdown(60); // 60秒倒计时
      setStep('verification');
      console.log('验证码发送成功，切换到验证步骤');
    } catch (error) {
      console.error('发送验证码失败:', error);
    }
  };

  // 验证验证码并进入密码设置步骤
  const handleVerifyCode = async () => {
    if (!formData.verificationCode || formData.verificationCode.length !== 6) {
      setFormErrors({ verificationCode: '请输入6位验证码' });
      return;
    }

    setVerifyLoading(true);
    try {
      // 调用后端API验证验证码
      await authApi.verifyCode(formData.email, formData.verificationCode, 'register');
      setStep('password');
      setFormErrors({});
    } catch (error: any) {
      setFormErrors({ verificationCode: error.message || '验证码无效或已过期' });
    } finally {
      setVerifyLoading(false);
    }
  };

  // 表单验证
  const validateForm = (): boolean => {
    const errors: FormErrors = {};

    // 邀请码验证
    if (step === 'invite' && !formData.inviteCode.trim()) {
      errors.inviteCode = '请输入邀请码';
    }

    // 邮箱验证（必填）
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email.trim()) {
      errors.email = '请输入邮箱地址';
    } else if (!emailRegex.test(formData.email)) {
      errors.email = '请输入有效的邮箱地址';
    }

    // 验证码验证
    if (step !== 'email' && !formData.verificationCode) {
      errors.verificationCode = '请输入验证码';
    } else if (formData.verificationCode && formData.verificationCode.length !== 6) {
      errors.verificationCode = '验证码必须是6位数字';
    }

    // 用户名验证
    if (step === 'password' && !formData.username.trim()) {
      errors.username = '请输入用户名';
    } else if (formData.username && formData.username.length < 3) {
      errors.username = '用户名至少需要3个字符';
    }

    // 密码验证
    if (step === 'password' && !formData.password) {
      errors.password = '请输入密码';
    } else if (formData.password) {
      // 简单的密码强度检查
      if (formData.password.length < 6) {
        errors.password = '密码至少需要6位字符';
      }
    }

    // 确认密码验证
    if (step === 'password' && !formData.confirmPassword) {
      errors.confirmPassword = '请确认密码';
    } else if (formData.password && formData.confirmPassword && formData.password !== formData.confirmPassword) {
      errors.confirmPassword = '两次输入的密码不一致';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('表单提交，当前步骤:', step);

    if (step === 'invite') {
      console.log('执行邀请码验证');
      await handleValidateInviteCode();
    } else if (step === 'email') {
      console.log('执行发送验证码');
      await handleSendCode();
    } else if (step === 'verification') {
      console.log('执行验证码验证');
      await handleVerifyCode();
    } else if (step === 'password') {
      console.log('执行注册');
      if (!validateForm()) {
        return;
      }

      try {
        await register(formData);
        // 注册成功后会自动登录并重定向
      } catch (error) {
        // 错误已经在AuthContext中处理
        console.error('Registration failed:', error);
      }
    }
  };

  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-slate-50"></div>
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-blue-100/30 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-to-tl from-slate-100/40 to-transparent rounded-full blur-3xl"></div>
      </div>

      {/* 网格背景 */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#f1f5f9_1px,transparent_1px),linear-gradient(to_bottom,#f1f5f9_1px,transparent_1px)] bg-[size:4rem_4rem] opacity-20"></div>

      <div className="relative z-10 min-h-screen flex items-center justify-center px-6 py-12">
        <div className="w-full max-w-md">
          {/* 品牌区域 */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-slate-900 to-slate-700 rounded-2xl flex items-center justify-center shadow-lg">
                <Sparkles className="w-7 h-7 text-white" />
              </div>
              <h1 className="text-2xl font-bold text-slate-900">XCoding</h1>
            </div>
            <h2 className="text-xl font-semibold text-slate-900 mb-2">创建账户</h2>
            <p className="text-slate-600">加入我们，开始您的AI开发之旅</p>
          </div>

          {/* 注册表单 */}
          <div className="bg-white rounded-2xl border border-slate-200 shadow-lg p-8">
            {/* 错误提示 */}
            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-red-800 text-sm font-medium">注册失败</p>
                  <p className="text-red-700 text-sm">{error}</p>
                </div>
              </div>
            )}

            {/* 步骤指示器 */}
            <div className="flex items-center justify-center mb-8">
              <div className="flex items-center space-x-4">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                  step === 'invite' ? 'bg-blue-500 text-white' :
                  ['email', 'verification', 'password'].includes(step) ? 'bg-green-500 text-white' : 'bg-slate-200 text-slate-600'
                }`}>
                  1
                </div>
                <div className={`w-8 h-0.5 ${
                  ['email', 'verification', 'password'].includes(step) ? 'bg-green-500' : 'bg-slate-200'
                }`} />
                <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                  step === 'email' ? 'bg-blue-500 text-white' :
                  ['verification', 'password'].includes(step) ? 'bg-green-500 text-white' : 'bg-slate-200 text-slate-600'
                }`}>
                  2
                </div>
                <div className={`w-8 h-0.5 ${
                  ['verification', 'password'].includes(step) ? 'bg-green-500' : 'bg-slate-200'
                }`} />
                <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                  step === 'verification' ? 'bg-blue-500 text-white' :
                  step === 'password' ? 'bg-green-500 text-white' : 'bg-slate-200 text-slate-600'
                }`}>
                  3
                </div>
                <div className={`w-8 h-0.5 ${
                  step === 'password' ? 'bg-green-500' : 'bg-slate-200'
                }`} />
                <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                  step === 'password' ? 'bg-blue-500 text-white' : 'bg-slate-200 text-slate-600'
                }`}>
                  4
                </div>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* 步骤1：邀请码验证 */}
              {step === 'invite' && (
                <div>
                  <label htmlFor="inviteCode" className="block text-sm font-medium text-slate-700 mb-2">
                    邀请码 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="inviteCode"
                    name="inviteCode"
                    value={formData.inviteCode}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors ${
                      formErrors.inviteCode ? 'border-red-300 bg-red-50' : ''
                    }`}
                    placeholder="请输入邀请码"
                    disabled={loading || verifyLoading}
                  />
                  {formErrors.inviteCode && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.inviteCode}</p>
                  )}
                  {inviteCodeValid && inviteCodeInfo && (
                    <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                      <p className="text-sm text-green-700">
                        ✓ 邀请码有效！来自用户：{inviteCodeInfo.creator?.username}
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* 步骤2：邮箱输入 */}
              {step === 'email' && (
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-slate-700 mb-2">
                    邮箱地址 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors ${
                      formErrors.email ? 'border-red-300 bg-red-50' : 'border-slate-300'
                    }`}
                    placeholder="请输入邮箱地址"
                    disabled={loading}
                  />
                  {formErrors.email && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.email}</p>
                  )}
                </div>
              )}

              {/* 步骤3：验证码输入 */}
              {step === 'verification' && (
                <div>
                  <label htmlFor="verificationCode" className="block text-sm font-medium text-slate-700 mb-2">
                    邮箱验证码 <span className="text-red-500">*</span>
                  </label>
                  <div className="flex space-x-3">
                    <input
                      type="text"
                      id="verificationCode"
                      name="verificationCode"
                      value={formData.verificationCode}
                      onChange={handleInputChange}
                      className={`flex-1 px-4 py-3 border rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors ${
                        formErrors.verificationCode ? 'border-red-300 bg-red-50' : 'border-slate-300'
                      }`}
                      placeholder="请输入6位验证码"
                      maxLength={6}
                      disabled={loading}
                    />
                    <button
                      type="button"
                      onClick={handleSendCode}
                      disabled={countdown > 0 || loading}
                      className="px-4 py-3 bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors whitespace-nowrap"
                    >
                      {countdown > 0 ? `${countdown}s` : '重新发送'}
                    </button>
                  </div>
                  {formErrors.verificationCode && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.verificationCode}</p>
                  )}
                  <p className="mt-2 text-sm text-slate-600">
                    验证码已发送到 {formData.email}，请查收邮件
                  </p>
                </div>
              )}

              {/* 步骤4：用户名和密码设置 */}
              {step === 'password' && (
                <>
                  {/* 用户名输入 */}
                  <div>
                    <label htmlFor="username" className="block text-sm font-medium text-slate-700 mb-2">
                      用户名 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="username"
                      name="username"
                      value={formData.username}
                      onChange={handleInputChange}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors ${
                        formErrors.username ? 'border-red-300 bg-red-50' : 'border-slate-300'
                      }`}
                      placeholder="请输入用户名"
                      disabled={loading}
                    />
                    {formErrors.username && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.username}</p>
                    )}
                  </div>

                  {/* 密码输入 */}
                  <div>
                    <label htmlFor="password" className="block text-sm font-medium text-slate-700 mb-2">
                      密码 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type={showPassword ? 'text' : 'password'}
                        id="password"
                        name="password"
                        value={formData.password}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 pr-12 border rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors ${
                          formErrors.password ? 'border-red-300 bg-red-50' : 'border-slate-300'
                        }`}
                        placeholder="请输入密码"
                        disabled={loading}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600"
                    disabled={loading}
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
                {formErrors.password && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.password}</p>
                )}
                {/* 密码强度提示 */}
                {passwordStrength.message && (
                  <div className={`mt-2 text-sm flex items-center space-x-2 ${
                    passwordStrength.valid ? 'text-green-600' : 'text-amber-600'
                  }`}>
                    {passwordStrength.valid ? (
                      <CheckCircle className="w-4 h-4" />
                    ) : (
                      <AlertCircle className="w-4 h-4" />
                    )}
                    <span>{passwordStrength.message}</span>
                  </div>
                  )}

                  {/* 确认密码输入 */}
                  <div>
                    <label htmlFor="confirmPassword" className="block text-sm font-medium text-slate-700 mb-2">
                      确认密码 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type={showConfirmPassword ? 'text' : 'password'}
                        id="confirmPassword"
                        name="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 pr-12 border rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors ${
                          formErrors.confirmPassword ? 'border-red-300 bg-red-50' : 'border-slate-300'
                        }`}
                        placeholder="请再次输入密码"
                        disabled={loading}
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600"
                        disabled={loading}
                      >
                        {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </button>
                    </div>
                    {formErrors.confirmPassword && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.confirmPassword}</p>
                    )}
                  </div>
                  </div>
                </>
              )}

              {/* 按钮组 */}
              <div className="space-y-3">
                {/* 返回按钮 */}
                {step !== 'invite' && (
                  <button
                    type="button"
                    onClick={() => {
                      if (step === 'email') setStep('invite');
                      if (step === 'verification') setStep('email');
                      if (step === 'password') setStep('verification');
                    }}
                    className="w-full bg-slate-100 hover:bg-slate-200 text-slate-700 font-medium py-3 px-4 rounded-lg transition-colors"
                  >
                    返回上一步
                  </button>
                )}

                {/* 提交按钮 */}
                <button
                  type="submit"
                  disabled={loading || verifyLoading || (step === 'invite' && !formData.inviteCode) || (step === 'email' && !formData.email) || (step === 'verification' && !formData.verificationCode) || (step === 'password' && (!formData.username || !passwordStrength.valid || !formData.confirmPassword))}
                  className="w-full bg-slate-900 hover:bg-slate-800 disabled:bg-slate-400 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center"
                >
                {(loading || verifyLoading) ? (
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                ) : (
                  step === 'invite' ? '验证邀请码' :
                  step === 'email' ? '发送验证码' :
                  step === 'verification' ? '验证邮箱' :
                  '完成注册'
                )}
                </button>
              </div>
            </form>

            {/* 分割线 */}
            <div className="my-6 flex items-center">
              <div className="flex-1 border-t border-slate-200"></div>
              <span className="px-4 text-sm text-slate-500">或</span>
              <div className="flex-1 border-t border-slate-200"></div>
            </div>

            {/* Google登录 */}
            <GoogleLoginButton
              onSuccess={async (credential) => {
                try {
                  await loginWithGoogle(credential);
                  // 登录成功后会自动跳转到dashboard
                } catch (error) {
                  console.error('Google注册失败:', error);
                }
              }}
              onError={(error) => {
                console.error('Google登录错误:', error);
              }}
              disabled={loading}
              text="使用 Google 注册"
            />

            {/* 登录链接 */}
            <div className="mt-6 text-center">
              <p className="text-slate-600 text-sm">
                已有账户？{' '}
                <Link
                  href="/login"
                  className="text-slate-900 font-medium hover:underline"
                >
                  立即登录
                </Link>
              </p>
            </div>
          </div>

          {/* 返回首页链接 */}
          <div className="mt-6 text-center">
            <Link
              href="/"
              className="text-slate-500 hover:text-slate-700 text-sm transition-colors"
            >
              ← 返回首页
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
