'use client';

import React, { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { useAuth } from '../contexts/auth-context';

declare global {
  interface Window {
    google?: any;
    handleOneTapCredentialResponse?: (response: any) => void;
  }
}

const GoogleOneTap: React.FC = () => {
  const pathname = usePathname();
  const { isAuthenticated, loginWithGoogle } = useAuth();

  useEffect(() => {
    // 延迟检查，确保认证状态已经初始化
    const checkAndShowOneTap = () => {
      const currentPath = window.location.pathname;

      // 只在首页时尝试One Tap
      if (currentPath !== '/') {
        return;
      }

      // 检查当前认证状态（从DOM或其他方式获取）
      // 这里我们假设如果页面上有登录按钮，说明用户未登录
      const hasLoginButton = document.querySelector('[href="/login"]');
      if (!hasLoginButton) {
        // console.log('未找到登录按钮，可能已登录，不显示One Tap');
        return;
      }

      console.log('检测到未登录状态，开始加载Google Identity Services');
      loadGoogleOneTap();
    };

    const loadGoogleOneTap = () => {
      console.log('开始加载Google Identity Services');

    // 定义回调函数
    window.handleOneTapCredentialResponse = async (response: any) => {
      try {

        if (window.google?.accounts) {
          window.google.accounts.id.cancel();
        }

        await loginWithGoogle(response.credential);
      } catch (error) {
        console.error('Google One Tap登录失败:', error);
      }
    };

    // 加载Google Identity Services脚本
    const script = document.createElement('script');
    script.src = 'https://accounts.google.com/gsi/client';
    script.async = true;
    script.defer = true;

    script.onload = () => {
      if (window.google?.accounts) {
        try {
          console.log('初始化Google Identity Services');
          window.google.accounts.id.initialize({
            client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
            callback: window.handleOneTapCredentialResponse,
            auto_select: false,
            cancel_on_tap_outside: true,
          });

          window.google.accounts.id.prompt((notification: any) => {

            if (notification.isNotDisplayed()) {
              console.log('One Tap未显示 - 可能FedCM被禁用或用户未登录Google');
            } else if (notification.isSkippedMoment()) {
              console.log('One Tap被跳过');
            } else if (notification.isDismissedMoment()) {
              console.log('One Tap被用户关闭');
            } else {
              console.log('One Tap显示成功');
            }
          });

        } catch (error) {
          console.error('Google Identity Services初始化失败:', error);
        }
      }
    };

    script.onerror = () => {
      console.error('Google Identity Services脚本加载失败');
    };

    document.head.appendChild(script);
    };

    // 延迟执行检查，确保DOM已加载
    setTimeout(checkAndShowOneTap, 100);

    return () => {
      window.handleOneTapCredentialResponse = undefined;
    };
  }, []); // 空依赖数组，只在组件挂载时执行一次

  // Google One Tap是自动显示的，不需要渲染UI
  return null;
};

export default GoogleOneTap;
