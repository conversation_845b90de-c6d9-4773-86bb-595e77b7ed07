'use client'

import React, { useEffect } from 'react';

interface GoogleLoginButtonProps {
  onSuccess: (credential: string) => void;
  onError?: (error: any) => void;
  disabled?: boolean;
  text?: string;
}

declare global {
  interface Window {
    google?: any;
    handleCredentialResponse?: (response: any) => void;
  }
}

const GoogleLoginButton: React.FC<GoogleLoginButtonProps> = ({
  onSuccess,
  onError,
  disabled = false,
  text = '使用 Google 登录'
}) => {

  useEffect(() => {
    // 定义全局回调函数
    window.handleCredentialResponse = (response: any) => {
      if (response.credential) {
        onSuccess(response.credential);
      } else {
        onError?.(new Error('No credential received'));
      }
    };

    // 加载Google Identity Services脚本
    const script = document.createElement('script');
    script.src = 'https://accounts.google.com/gsi/client';
    script.async = true;
    script.defer = true;

    script.onload = () => {
      if (window.google) {
        try {
          window.google.accounts.id.initialize({
            client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
            callback: window.handleCredentialResponse,
            auto_select: false,
            cancel_on_tap_outside: true,
          });

          // 渲染隐藏的Google按钮
          setTimeout(() => {
            const buttonContainer = document.getElementById('google-signin-button');
            if (buttonContainer) {
              window.google.accounts.id.renderButton(buttonContainer, {
                theme: 'outline',
                size: 'large',
                text: 'signin_with',
                shape: 'rectangular',
                logo_alignment: 'left',
                width: 320,
              });
            }
          }, 100);

          console.log('Google Identity Services initialized');
        } catch (error) {
          console.error('Google initialization error:', error);
          onError?.(error);
        }
      }
    };

    script.onerror = () => {
      onError?.(new Error('Failed to load Google Identity Services'));
    };

    document.head.appendChild(script);

    return () => {
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
      delete window.handleCredentialResponse;
    };
  }, [onSuccess, onError]);

  return (
    <div className="w-full relative">
      {/* 自定义样式按钮（显示层） */}
      <button
        type="button"
        disabled={disabled}
        className="w-full h-11 border border-slate-300 rounded-lg bg-white text-slate-700 font-medium flex items-center justify-center space-x-3 hover:bg-slate-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed pointer-events-none"
      >
        <svg className="w-5 h-5" viewBox="0 0 24 24">
          <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
          <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
          <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
          <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
        </svg>
        <span>{text}</span>
      </button>

      {/* 隐藏的Google原生按钮（功能层） */}
      <div
        id="google-signin-button"
        className={`absolute inset-0 opacity-0 ${disabled ? 'pointer-events-none' : ''}`}
        style={{ zIndex: 10 }}
      />

      {!process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID && (
        <div className="mt-2 text-sm text-red-600 text-center">
          Google Client ID not configured
        </div>
      )}
    </div>
  );
};

export default GoogleLoginButton;
