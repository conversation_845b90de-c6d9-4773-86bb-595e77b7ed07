'use client'

import React, { createContext, useContext, useReducer, useEffect, useCallback, ReactNode } from 'react';
import type { User, AuthState, LoginCredentials, RegisterData } from '../types';
import { authApi, httpClient } from '../lib/api';
import { storage, errorUtils } from '../lib/utils';

// 认证上下文类型
export interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  sendVerificationCode: (email: string, type: 'register' | 'login' | 'reset_password') => Promise<void>;
  loginWithGoogle: (credential: string) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  clearError: () => void;
}

// 认证动作类型
type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: { user: User; token: string } }
  | { type: 'AUTH_FAILURE'; payload: string }
  | { type: 'AUTH_LOGOUT' }
  | { type: 'AUTH_STOP_LOADING' }
  | { type: 'CLEAR_ERROR' };

// 初始状态 - 从localStorage恢复
const getInitialState = (): AuthState => {
  try {
    const token = storage.get<string>('auth_token');
    const user = storage.get<User>('auth_user');

    if (token && user && typeof token === 'string' && typeof user === 'object') {
      // 设置token到HTTP客户端
      httpClient.setToken(token);
      return {
        isAuthenticated: true,
        user,
        token,
        loading: false,
        error: null,
      };
    }
  } catch (error) {
    console.warn('AuthContext: Failed to restore auth state from localStorage, clearing data:', error);
    // 清除损坏的数据
    storage.remove('auth_token');
    storage.remove('auth_user');
  }

  return {
    isAuthenticated: false,
    user: null,
    token: null,
    loading: false,
    error: null,
  };
};

// 认证状态reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        loading: true,
        error: null,
      };

    case 'AUTH_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        token: action.payload.token,
        loading: false,
        error: null,
      };

    case 'AUTH_FAILURE':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
        error: action.payload,
      };

    case 'AUTH_STOP_LOADING':
      return {
        ...state,
        loading: false,
      };

    case 'AUTH_LOGOUT':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
        error: null,
      };

    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };

    default:
      return state;
  }
};

// 创建认证上下文
export const AuthContext = createContext<AuthContextType | undefined>(undefined);

// 认证提供者组件
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, getInitialState());

  // 处理认证成功
  const handleAuthSuccess = useCallback((user: User, token: string) => {
    
    // 保存到本地存储
    storage.set('auth_token', token);
    storage.set('auth_user', user);
    
    // 设置HTTP客户端token
    httpClient.setToken(token);
    
    // 更新状态
    dispatch({
      type: 'AUTH_SUCCESS',
      payload: { user, token },
    });
  }, []);

  // 处理认证失败
  const handleAuthFailure = useCallback((error: unknown) => {
    const errorMessage = errorUtils.getMessage(error);
    console.error('AuthContext: Authentication failed:', errorMessage);
    
    dispatch({
      type: 'AUTH_FAILURE',
      payload: errorMessage,
    });
  }, []);

  // 处理登出
  const handleLogout = useCallback(() => {
    
    // 清除本地存储
    storage.remove('auth_token');
    storage.remove('auth_user');
    
    // 更新状态
    dispatch({ type: 'AUTH_LOGOUT' });
  }, []);

  // 登录
  const login = async (credentials: LoginCredentials): Promise<void> => {
    try {
      dispatch({ type: 'AUTH_START' });

      const response = await authApi.login(credentials);
      handleAuthSuccess(response.user, response.token);
    } catch (error) {
      handleAuthFailure(error);
      throw error;
    }
  };

  // 发送邮箱验证码
  const sendVerificationCode = async (email: string, type: 'register' | 'login' | 'reset_password'): Promise<void> => {
    try {
      dispatch({ type: 'AUTH_START' });
      const response = await authApi.sendVerificationCode(email, type);
      // 发送验证码成功，但不改变认证状态，只停止loading
      dispatch({
        type: 'AUTH_STOP_LOADING'
      });
    } catch (error) {
      handleAuthFailure(error);
      throw error;
    }
  };

  // 注册
  const register = async (data: RegisterData): Promise<void> => {
    try {
      console.log('AuthContext: Register attempt for email:', data.email);
      dispatch({ type: 'AUTH_START' });

      const response = await authApi.register(data);

      // 注册成功后直接设置用户信息（后端已返回token）
      handleAuthSuccess(response.user, response.token);
    } catch (error) {
      handleAuthFailure(error);
      throw error;
    }
  };

  // Google登录
  const loginWithGoogle = async (credential: string): Promise<void> => {
    try {
      dispatch({ type: 'AUTH_START' });

      const response = await authApi.loginWithGoogle(credential);
      handleAuthSuccess(response.user, response.token);
    } catch (error) {
      handleAuthFailure(error);
      throw error;
    }
  };

  const logout = (): void => {
    authApi.logout();
    handleLogout();
  };

  // 刷新token
  const refreshToken = async (): Promise<void> => {
    try {
      const response = await authApi.refreshToken();
      const newToken = response.token;

      // 更新本地存储
      storage.set('auth_token', newToken);

      // 更新HTTP客户端token
      httpClient.setToken(newToken);

      // 更新状态
      if (state.user) {
        dispatch({
          type: 'AUTH_SUCCESS',
          payload: { user: state.user, token: newToken },
        });
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      handleLogout();
      throw error;
    }
  };

  // 清除错误
  const clearError = (): void => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const value: AuthContextType = {
    ...state,
    login,
    register,
    sendVerificationCode,
    loginWithGoogle,
    logout,
    refreshToken,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// useAuth hook
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
